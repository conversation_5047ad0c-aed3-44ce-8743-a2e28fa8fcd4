# -*- coding: utf-8 -*-
"""
Created on 29.11.2020

@author: <PERSON>
"""

import pandas
import datetime
import koalafolio.gui.QLogger as logger
from typing import Tuple, List, Optional, Dict, Any
from decimal import Decimal

localLogger = logger.globalLogger

try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    localLogger.warning("CCXT library not available. Install with: pip install ccxt")

# timestamp,  location,   pair,   trade_type, amount, rate,   fee,    fee_currency,   link
def tradesToDataframe(rotkiTrades):
    trades = []
    for rotkiTrade in rotkiTrades:
        trade = {}
        trade['timestamp'] = datetime.datetime.fromtimestamp(rotkiTrade.timestamp)
        trade['location'] = str(rotkiTrade.location)
        trade['pair'] = str(rotkiTrade.pair)
        trade['trade_type'] = str(rotkiTrade.trade_type)
        trade['amount'] = float(rotkiTrade.amount)
        trade['rate'] = float(rotkiTrade.rate)
        trade['fee'] = float(rotkiTrade.fee)
        trade['fee_currency'] = str(rotkiTrade.fee_currency.symbol)
        trade['link'] = str(rotkiTrade.link)
        trades.append(trade)
    return pandas.DataFrame(trades)

def getTradeHistoryCcxt(apiname, key, secret, start, end):
    api = ccxtExchange(apiname, api_key=key, api_secret=secret.encode())
    iskeyvalid, checkKeyMsg = api.validate_api_credentials()
    if iskeyvalid:
        trades = api.query_trade_history(start_ts=start, end_ts=end)
        tradesDF = tradesToDataframe(trades)
        return tradesDF
    localLogger.warning("api key is invalid for " + str(api.__class__.__name__) + ": " + checkKeyMsg)
    return pandas.DataFrame()

class ccxtExchange:
    """
    CCXT-based exchange implementation for unified API access

    Supports: binance, bitmex, coinbase, coinbasepro, gemini, poloniex, kraken
    """

    # Mapping of exchange names to CCXT classes
    SUPPORTED_EXCHANGES = {
        'binance': 'binance',
        'bitmex': 'bitmex',
        'coinbase': 'coinbase',
        'coinbasepro': 'coinbasepro',
        'gemini': 'gemini',
        'poloniex': 'poloniex',
        'kraken': 'kraken'
    }

    def __init__(self, exchange_name: str, api_key: str, api_secret: bytes, **kwargs):
        """
        Initialize CCXT exchange

        Args:
            exchange_name: Name of the exchange (e.g., 'binance', 'kraken')
            api_key: API key for the exchange
            api_secret: API secret (as bytes)
            **kwargs: Additional parameters (sandbox, passphrase, etc.)
        """
        if not CCXT_AVAILABLE:
            raise ImportError("CCXT library is required. Install with: pip install ccxt")

        self.exchange_name = exchange_name.lower()
        self.api_key = api_key
        self.api_secret = api_secret.decode() if isinstance(api_secret, bytes) else api_secret

        # Validate exchange name
        if self.exchange_name not in self.SUPPORTED_EXCHANGES:
            raise ValueError(f"Unsupported exchange: {exchange_name}. Supported: {list(self.SUPPORTED_EXCHANGES.keys())}")

        # Get CCXT exchange class
        ccxt_exchange_name = self.SUPPORTED_EXCHANGES[self.exchange_name]
        exchange_class = getattr(ccxt, ccxt_exchange_name)

        # Initialize exchange with credentials
        config = {
            'apiKey': self.api_key,
            'secret': self.api_secret,
            'timeout': 30000,  # 30 seconds
            'enableRateLimit': True,
        }

        # Add exchange-specific configurations
        if self.exchange_name == 'coinbasepro':
            # Coinbase Pro requires passphrase
            config['passphrase'] = kwargs.get('passphrase', '')
        elif self.exchange_name == 'bitmex':
            # BitMEX might need testnet configuration
            config['sandbox'] = kwargs.get('sandbox', False)

        # Add sandbox mode if specified
        if kwargs.get('sandbox', False):
            config['sandbox'] = True

        try:
            self.exchange = exchange_class(config)
            localLogger.info(f"Initialized CCXT exchange: {self.exchange_name}")
        except Exception as e:
            localLogger.error(f"Failed to initialize {self.exchange_name}: {str(e)}")
            raise

    def validate_api_credentials(self) -> Tuple[bool, str]:
        """
        Validate API credentials by making a test request

        Returns:
            Tuple of (is_valid: bool, message: str)
        """
        try:
            # Try to fetch account balance as a credential test
            if hasattr(self.exchange, 'fetch_balance'):
                balance = self.exchange.fetch_balance()
                return True, "API credentials are valid"
            else:
                # Fallback: try to fetch ticker data (public endpoint)
                markets = self.exchange.load_markets()
                if markets:
                    return True, "API credentials appear valid (public access confirmed)"
                else:
                    return False, "Could not access exchange API"

        except ccxt.AuthenticationError as e:
            return False, f"Authentication failed: {str(e)}"
        except ccxt.PermissionDenied as e:
            return False, f"Permission denied: {str(e)}"
        except ccxt.InvalidNonce as e:
            return False, f"Invalid nonce (check system time): {str(e)}"
        except ccxt.InsufficientFunds as e:
            # This actually means credentials work, just no funds
            return True, "API credentials are valid (insufficient funds is normal)"
        except ccxt.NetworkError as e:
            return False, f"Network error: {str(e)}"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"

    def query_trade_history(self, start_ts: Optional[int] = None, end_ts: Optional[int] = None) -> List[Any]:
        """
        Query trade history from the exchange

        Args:
            start_ts: Start timestamp (Unix timestamp in seconds)
            end_ts: End timestamp (Unix timestamp in seconds)

        Returns:
            List of trade objects compatible with rotki format
        """
        try:
            # Load markets first
            self.exchange.load_markets()

            # Convert timestamps to milliseconds (CCXT uses milliseconds)
            since = int(start_ts * 1000) if start_ts else None
            until = int(end_ts * 1000) if end_ts else None

            # Fetch trades
            if hasattr(self.exchange, 'fetch_my_trades'):
                # Get all trades (some exchanges support symbol=None for all pairs)
                try:
                    trades = self.exchange.fetch_my_trades(symbol=None, since=since, limit=None)
                except Exception:
                    # If fetching all trades fails, try fetching per symbol
                    trades = self._fetch_trades_per_symbol(since, until)
            else:
                raise NotImplementedError(f"Exchange {self.exchange_name} does not support trade history")

            # Filter by end timestamp if specified
            if until:
                trades = [t for t in trades if t['timestamp'] <= until]

            # Convert to rotki-compatible format
            rotki_trades = []
            for trade in trades:
                rotki_trade = self._convert_to_rotki_format(trade)
                if rotki_trade:
                    rotki_trades.append(rotki_trade)

            localLogger.info(f"Retrieved {len(rotki_trades)} trades from {self.exchange_name}")
            return rotki_trades

        except Exception as e:
            localLogger.error(f"Failed to query trade history from {self.exchange_name}: {str(e)}")
            raise

    def _fetch_trades_per_symbol(self, since: Optional[int], until: Optional[int]) -> List[Dict[str, Any]]:
        """
        Fetch trades for each symbol individually (fallback method)
        """
        all_trades = []

        try:
            # Get all available markets
            markets = self.exchange.markets
            symbols = list(markets.keys())

            localLogger.info(f"Fetching trades for {len(symbols)} symbols from {self.exchange_name}")

            for symbol in symbols:
                try:
                    symbol_trades = self.exchange.fetch_my_trades(symbol, since=since, limit=None)
                    all_trades.extend(symbol_trades)
                except Exception as e:
                    # Some symbols might not have trades or might be delisted
                    localLogger.debug(f"Could not fetch trades for {symbol}: {str(e)}")
                    continue

        except Exception as e:
            localLogger.error(f"Error fetching trades per symbol: {str(e)}")

        return all_trades

    def _convert_to_rotki_format(self, ccxt_trade: Dict[str, Any]) -> Optional[Any]:
        """
        Convert CCXT trade format to rotki-compatible format

        Args:
            ccxt_trade: Trade data from CCXT

        Returns:
            Trade object compatible with rotki format
        """
        try:
            # Create a simple trade object that matches the expected interface
            class RotkiTrade:
                def __init__(self, trade_data, exchange_name):
                    self.exchange_name = exchange_name

                    # Convert timestamp from milliseconds to seconds
                    self.timestamp = trade_data['timestamp'] / 1000 if trade_data['timestamp'] else 0

                    # Map exchange name to location
                    self.location = self._map_exchange_to_location(trade_data.get('symbol', ''))

                    # Parse trading pair
                    symbol = trade_data.get('symbol', '')
                    if '/' in symbol:
                        base, quote = symbol.split('/')
                        self.pair = f"{base}_{quote}"
                    else:
                        self.pair = symbol

                    # Map trade side to type
                    side = trade_data.get('side', '').lower()
                    self.trade_type = 'buy' if side == 'buy' else 'sell'

                    # Trade amounts and rates
                    self.amount = Decimal(str(trade_data.get('amount', 0)))
                    self.rate = Decimal(str(trade_data.get('price', 0)))

                    # Fee information
                    fee_info = trade_data.get('fee', {})
                    if fee_info:
                        self.fee = Decimal(str(fee_info.get('cost', 0)))
                        self.fee_currency = self._create_asset(fee_info.get('currency', ''))
                    else:
                        self.fee = Decimal('0')
                        self.fee_currency = self._create_asset('USD')  # Default

                    # Trade ID/link
                    self.link = str(trade_data.get('id', ''))

                def _map_exchange_to_location(self, symbol):
                    """Map exchange name to location string"""
                    location_map = {
                        'binance': 'binance',
                        'bitmex': 'bitmex',
                        'coinbase': 'coinbase',
                        'coinbasepro': 'coinbasepro',
                        'gemini': 'gemini',
                        'poloniex': 'poloniex',
                        'kraken': 'kraken'
                    }
                    return location_map.get(self.exchange_name, self.exchange_name)

                def _create_asset(self, symbol):
                    """Create asset object"""
                    class Asset:
                        def __init__(self, symbol):
                            self.symbol = symbol.upper() if symbol else 'UNKNOWN'
                    return Asset(symbol)

            # Create the trade object
            trade = RotkiTrade(ccxt_trade, self.exchange_name)

            return trade

        except Exception as e:
            localLogger.error(f"Error converting trade to rotki format: {str(e)}")
            return None

    def get_supported_exchanges(self) -> List[str]:
        """Get list of supported exchange names"""
        return list(self.SUPPORTED_EXCHANGES.keys())

    def __str__(self) -> str:
        return f"ccxtExchange({self.exchange_name})"

    def __repr__(self) -> str:
        return f"ccxtExchange(exchange_name='{self.exchange_name}', api_key='***')"






