"""
Example usage of the exchange APIs library
"""
import os
from datetime import datetime, timedelta
from exchange_apis.core.types import Timestamp
from exchange_apis.exchanges.kraken import KrakenExchange
from exchange_apis.exchanges.binance import BinanceExchange
from exchange_apis.exchanges.coinbase import CoinbaseExchange


def main():
    """Example usage of the exchange APIs"""
    
    # Example 1: Kraken
    print("=== Kraken Example ===")
    
    # Initialize Kraken exchange
    kraken = KrakenExchange(
        name="my_kraken",
        api_key=os.getenv("KRAKEN_API_KEY", "your_api_key"),
        api_secret=os.getenv("KRAKEN_API_SECRET", "your_api_secret").encode(),
    )
    
    # Validate credentials
    is_valid, message = kraken.validate_api_credentials()
    print(f"Kraken credentials valid: {is_valid} - {message}")
    
    if is_valid:
        # Query trade history for the last 30 days
        end_time = Timestamp(int(datetime.now().timestamp()))
        start_time = Timestamp(int((datetime.now() - timedelta(days=30)).timestamp()))
        
        trades = kraken.query_trade_history(start_time, end_time)
        print(f"Found {len(trades)} Kraken trades")
        
        for trade in trades[:5]:  # Show first 5 trades
            print(f"  {trade}")
    
    print()
    
    # Example 2: Binance
    print("=== Binance Example ===")
    
    binance = BinanceExchange(
        name="my_binance",
        api_key=os.getenv("BINANCE_API_KEY", "your_api_key"),
        api_secret=os.getenv("BINANCE_API_SECRET", "your_api_secret").encode(),
    )
    
    is_valid, message = binance.validate_api_credentials()
    print(f"Binance credentials valid: {is_valid} - {message}")
    
    if is_valid:
        trades = binance.query_trade_history(start_time, end_time)
        print(f"Found {len(trades)} Binance trades")
        
        for trade in trades[:5]:
            print(f"  {trade}")
    
    print()
    
    # Example 3: Coinbase
    print("=== Coinbase Example ===")
    
    coinbase = CoinbaseExchange(
        name="my_coinbase",
        api_key=os.getenv("COINBASE_API_KEY", "your_api_key"),
        api_secret=os.getenv("COINBASE_API_SECRET", "your_api_secret").encode(),
        passphrase=os.getenv("COINBASE_PASSPHRASE", "your_passphrase"),
    )
    
    is_valid, message = coinbase.validate_api_credentials()
    print(f"Coinbase credentials valid: {is_valid} - {message}")
    
    if is_valid:
        trades = coinbase.query_trade_history(start_time, end_time)
        print(f"Found {len(trades)} Coinbase trades")
        
        for trade in trades[:5]:
            print(f"  {trade}")
    
    print()
    
    # Example 4: Export trades to JSON
    print("=== Export Example ===")
    
    all_trades = []
    
    # Collect trades from all exchanges (if credentials are valid)
    for exchange in [kraken, binance, coinbase]:
        try:
            is_valid, _ = exchange.validate_api_credentials()
            if is_valid:
                trades = exchange.query_trade_history(start_time, end_time)
                all_trades.extend(trades)
        except Exception as e:
            print(f"Error with {exchange.name}: {e}")
    
    # Sort all trades by timestamp
    all_trades.sort(key=lambda t: t.timestamp)
    
    print(f"Total trades from all exchanges: {len(all_trades)}")
    
    # Export to JSON file
    if all_trades:
        import json
        
        trades_data = [trade.to_dict() for trade in all_trades]
        
        with open('exported_trades.json', 'w') as f:
            json.dump(trades_data, f, indent=2)
        
        print("Trades exported to 'exported_trades.json'")
        
        # Show summary by exchange
        from collections import Counter
        exchange_counts = Counter(trade.location.name for trade in all_trades)
        
        print("\nTrades by exchange:")
        for exchange, count in exchange_counts.items():
            print(f"  {exchange}: {count} trades")


if __name__ == "__main__":
    main()
