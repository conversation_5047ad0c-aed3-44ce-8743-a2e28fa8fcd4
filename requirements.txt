gevent==24.11.1
greenlet==3.1.1
gevent-websocket==0.10.1  # like the below is abandoned
wsaccel==0.6.7  # recommended for acceleration of gevent-websocket. But abandoned.
web3==7.8.0
eth-typing==5.2.1
content-hash==2.0.0
rotki-pysqlcipher3==2024.10.1
requests==2.32.3
urllib3==2.3.0
coincurve==20.0.0
base58check==1.0.2
bech32==1.2.0
jsonschema==4.23.0
gql[requests]==3.5.0
scalecodec==1.2.11
py-sr25519-bindings==0.2.0
py-ed25519-zebra-bindings==1.0.1
py-bip39-bindings==0.1.11
substrate-interface==1.7.11
beautifulsoup4==4.12.3
maxminddb==2.6.3
pywin32==306; sys_platform == 'win32'
miniupnpc==2.2.8
cryptography==44.0.0
py-machineid==0.6.0
more-itertools==10.6.0  # for peekable iterators
regex==2024.11.6  # used for unicode information in detection of spam tokens
eth-abi==5.2.0
polars-lts-cpu==1.22.0
eth-utils==5.3.0

# For the rest api
flask-cors==5.0.0
flask==3.1.0
marshmallow==3.26.1
webargs==8.6.0
werkzeug==3.1.3

# for icon validation
filetype==1.2.0

# for fuzzy search of assets
polyleven==0.8

# We used to only use this for packaging, but now use it for the version comparison functionality
packaging==24.0

bip-utils==2.9.3

#constraints
pycparser<=2.17 # ugly -- to avoid https://github.com/eliben/pycparser/pull/198

# To build JWT for coinbase exchange key validation
PyJWT==2.10.1
