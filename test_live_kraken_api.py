#!/usr/bin/env python3
"""
Test script for live Kraken API integration

This script tests the updated exchange_apis implementation that uses
live Kraken API endpoints to fetch Assets and AssetPairs data.

Usage:
    python test_live_kraken_api.py
"""

import sys
from pathlib import Path

# Add the exchange_apis to the path
sys.path.insert(0, str(Path(__file__).parent))

from exchange_apis.exchanges.kraken import KrakenExchange
from exchange_apis.core.types import Location


def test_live_api_integration():
    """Test the live API integration"""
    print("=" * 80)
    print("Live Kraken API Integration Test")
    print("=" * 80)
    
    # Initialize Kraken exchange (dummy credentials for API data loading)
    kraken = KrakenExchange(
        name="test_kraken",
        api_key="dummy_key",
        api_secret=b"dummy_secret",
    )
    
    print("\n1. Testing API data loading...")
    
    # Load API data
    kraken._load_api_data()
    
    # Check if data was loaded
    print(f"   Assets loaded: {len(kraken.assets)}")
    print(f"   Asset pairs loaded: {len(kraken.asset_pairs)}")
    print(f"   Asset name mappings: {len(kraken.asset_name_map)}")
    print(f"   Parser known pairs: {len(kraken.pair_parser.known_pairs)}")
    
    if len(kraken.assets) == 0 or len(kraken.asset_pairs) == 0:
        print("❌ Failed to load API data")
        return False
    
    print("✅ API data loaded successfully")
    
    print("\n2. Testing asset information...")
    
    # Test some asset lookups
    test_assets = ['XXBT', 'XETH', 'ZUSD', 'ZEUR', 'ADA', 'DOT']
    for asset in test_assets:
        info = kraken.get_asset_info(asset)
        if info:
            altname = info.get('altname', asset)
            print(f"   {asset:8} -> {altname:8} (altname)")
        else:
            print(f"   {asset:8} -> NOT FOUND")
    
    print("\n3. Testing pair parsing with live API data...")
    
    # Test complex pairs that were problematic before
    test_pairs = [
        'XXBTZUSD', 'XXBTZEUR', 'XETHZUSD', 'XETHZEUR',
        'ADAUSD', 'DOTUSD', 'LINKETH', 'ATOMXBT',
        'ZEURZUSD', 'ZUSDZCAD', 'XXDGXXBT'
    ]
    
    successful_parses = 0
    for pair in test_pairs:
        try:
            base_asset, quote_asset = kraken.pair_parser.parse_pair(pair)
            result = f"{base_asset.identifier}_{quote_asset.identifier}"
            print(f"   ✅ {pair:12} -> {result}")
            successful_parses += 1
        except Exception as e:
            print(f"   ❌ {pair:12} -> ERROR: {e}")
    
    print(f"\n   Parsing success rate: {successful_parses}/{len(test_pairs)} ({successful_parses/len(test_pairs)*100:.1f}%)")
    
    print("\n4. Testing supported pairs...")
    
    # Get all supported pairs
    supported_pairs = kraken.get_supported_pairs()
    print(f"   Total supported pairs: {len(supported_pairs)}")
    
    # Show first 20 pairs
    print("   Sample pairs:")
    for pair in supported_pairs[:20]:
        try:
            base_asset, quote_asset = kraken.pair_parser.parse_pair(pair)
            result = f"{base_asset.identifier}_{quote_asset.identifier}"
            print(f"     {pair:12} -> {result}")
        except Exception as e:
            print(f"     {pair:12} -> ERROR: {e}")
    
    if len(supported_pairs) > 20:
        print(f"     ... and {len(supported_pairs) - 20} more pairs")
    
    print("\n5. Testing asset name mapping...")
    
    # Show some asset name mappings
    sample_mappings = list(kraken.asset_name_map.items())[:15]
    print("   Sample asset mappings:")
    for kraken_name, standard_name in sample_mappings:
        print(f"     {kraken_name:12} -> {standard_name}")
    
    print(f"\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    print(f"✅ Assets loaded: {len(kraken.assets)}")
    print(f"✅ Asset pairs loaded: {len(kraken.asset_pairs)}")
    print(f"✅ Pair parsing success: {successful_parses}/{len(test_pairs)}")
    print(f"✅ Total supported pairs: {len(supported_pairs)}")
    
    if successful_parses == len(test_pairs) and len(supported_pairs) > 1000:
        print("\n🎉 Live API integration working perfectly! 🎉")
        return True
    else:
        print("\n⚠️  Some issues detected - check the output above")
        return False


def test_pair_parsing_accuracy():
    """Test parsing accuracy on a larger sample"""
    print("\n" + "=" * 80)
    print("COMPREHENSIVE PAIR PARSING TEST")
    print("=" * 80)
    
    kraken = KrakenExchange("test", "dummy", b"dummy")
    kraken._load_api_data()
    
    all_pairs = kraken.get_supported_pairs()
    print(f"Testing parsing accuracy on all {len(all_pairs)} pairs...")
    
    successful = 0
    failed = 0
    
    for i, pair in enumerate(all_pairs):
        try:
            base_asset, quote_asset = kraken.pair_parser.parse_pair(pair)
            successful += 1
            
            # Show progress every 100 pairs
            if (i + 1) % 100 == 0:
                print(f"  Processed {i + 1}/{len(all_pairs)} pairs...")
                
        except Exception:
            failed += 1
    
    success_rate = (successful / len(all_pairs)) * 100
    
    print(f"\nResults:")
    print(f"  Total pairs: {len(all_pairs)}")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    print(f"  Success rate: {success_rate:.1f}%")
    
    if success_rate >= 99.0:
        print("🎉 Excellent parsing accuracy!")
    elif success_rate >= 95.0:
        print("✅ Good parsing accuracy")
    else:
        print("⚠️  Parsing accuracy needs improvement")
    
    return success_rate >= 99.0


if __name__ == "__main__":
    print("Testing live Kraken API integration...")
    
    # Test basic integration
    basic_success = test_live_api_integration()
    
    # Test comprehensive parsing
    if basic_success:
        comprehensive_success = test_pair_parsing_accuracy()
        overall_success = basic_success and comprehensive_success
    else:
        overall_success = False
    
    print(f"\n" + "=" * 80)
    if overall_success:
        print("🎉 ALL TESTS PASSED - Live API integration is working perfectly! 🎉")
    else:
        print("❌ Some tests failed - check the output above for details")
    
    print("\nThe exchange_apis library now uses live Kraken API data for:")
    print("- Asset information and name mapping")
    print("- Trading pair definitions and parsing")
    print("- Real-time accuracy with Kraken's current offerings")
    print("=" * 80)
    
    sys.exit(0 if overall_success else 1)
