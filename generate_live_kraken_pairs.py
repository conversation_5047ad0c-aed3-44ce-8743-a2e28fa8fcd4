#!/usr/bin/env python3
"""
Generate CSV file with all current Kraken pairs using live API

This script uses the updated exchange_apis implementation to:
1. Fetch live data from Kraken API endpoints
2. Parse all current trading pairs
3. Generate a CSV file for manual verification

Usage:
    python generate_live_kraken_pairs.py
"""

import csv
import sys
from pathlib import Path
from datetime import datetime

# Add the exchange_apis to the path
sys.path.insert(0, str(Path(__file__).parent))

from exchange_apis.exchanges.kraken import KrakenExchange


def generate_kraken_pairs_csv():
    """Generate CSV file with all current Kraken pairs"""
    print("=" * 80)
    print("Live Kraken Pairs CSV Generator")
    print("=" * 80)
    
    # Initialize Kraken exchange
    kraken = KrakenExchange(
        name="live_kraken",
        api_key="dummy_key",  # Not needed for public API endpoints
        api_secret=b"dummy_secret",
    )
    
    print("\n1. Fetching live data from Kraken API...")
    
    # Load live API data
    kraken._load_api_data()
    
    if len(kraken.asset_pairs) == 0:
        print("❌ Failed to load API data")
        return False
    
    print(f"   ✅ Loaded {len(kraken.assets)} assets")
    print(f"   ✅ Loaded {len(kraken.asset_pairs)} trading pairs")
    
    print("\n2. Parsing all trading pairs...")
    
    # Get all supported pairs
    all_pairs = kraken.get_supported_pairs()
    results = []
    successful_parses = 0
    failed_parses = 0
    
    for i, pair in enumerate(all_pairs):
        try:
            base_asset, quote_asset = kraken.pair_parser.parse_pair(pair)
            result_format = f"{base_asset.identifier}_{quote_asset.identifier}"
            
            results.append({
                'pair': pair,
                'parsed': result_format,
                'base': base_asset.identifier,
                'quote': quote_asset.identifier,
                'status': 'SUCCESS'
            })
            successful_parses += 1
            
            # Show progress every 100 pairs
            if (i + 1) % 100 == 0:
                print(f"   Processed {i + 1}/{len(all_pairs)} pairs...")
                
        except Exception as e:
            results.append({
                'pair': pair,
                'parsed': f'ERROR: {str(e)}',
                'base': 'ERROR',
                'quote': 'ERROR',
                'status': 'FAILED'
            })
            failed_parses += 1
    
    print(f"\n   ✅ Parsing complete: {successful_parses}/{len(all_pairs)} successful ({successful_parses/len(all_pairs)*100:.1f}%)")
    
    print("\n3. Generating CSV file...")
    
    # Generate timestamp for filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"kraken_live_pairs_{timestamp}.csv"
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow(['pair', 'parsed_result'])
            
            # Write data
            for result in results:
                writer.writerow([result['pair'], result['parsed']])
        
        print(f"   ✅ CSV file generated: {output_file}")
        
    except Exception as e:
        print(f"   ❌ Error generating CSV: {e}")
        return False
    
    print("\n4. Summary statistics...")
    
    # Count different asset types
    base_assets = set()
    quote_assets = set()
    fiat_quotes = set()
    crypto_quotes = set()
    
    fiat_currencies = {'USD', 'EUR', 'GBP', 'CAD', 'JPY', 'CHF', 'AUD'}
    
    for result in results:
        if result['status'] == 'SUCCESS':
            base_assets.add(result['base'])
            quote_assets.add(result['quote'])
            
            if result['quote'] in fiat_currencies:
                fiat_quotes.add(result['quote'])
            else:
                crypto_quotes.add(result['quote'])
    
    print(f"   Total pairs: {len(all_pairs)}")
    print(f"   Successful parses: {successful_parses}")
    print(f"   Failed parses: {failed_parses}")
    print(f"   Unique base assets: {len(base_assets)}")
    print(f"   Unique quote assets: {len(quote_assets)}")
    print(f"   Fiat quote currencies: {len(fiat_quotes)} ({', '.join(sorted(fiat_quotes))})")
    print(f"   Crypto quote assets: {len(crypto_quotes)}")
    
    print("\n5. Sample results...")
    
    # Show some sample results
    print("   Complex pairs (first 10):")
    complex_pairs = [r for r in results if 'XX' in r['pair'] or 'Z' in r['pair']][:10]
    for result in complex_pairs:
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
        print(f"     {status_icon} {result['pair']:12} -> {result['parsed']}")
    
    print("\n   Recent/Popular pairs (sample):")
    popular_pairs = [r for r in results if any(asset in r['pair'] for asset in ['BTC', 'ETH', 'ADA', 'DOT', 'LINK'])][:10]
    for result in popular_pairs:
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
        print(f"     {status_icon} {result['pair']:12} -> {result['parsed']}")
    
    print(f"\n" + "=" * 80)
    print("GENERATION COMPLETE")
    print("=" * 80)
    print(f"📁 Output file: {output_file}")
    print(f"📊 Format: pair,base_quote")
    print(f"✅ Success rate: {successful_parses/len(all_pairs)*100:.1f}%")
    print(f"🔄 Data source: Live Kraken API")
    print(f"⏰ Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if failed_parses == 0:
        print("\n🎉 Perfect! All pairs parsed successfully!")
    else:
        print(f"\n⚠️  {failed_parses} pairs failed to parse")
    
    print("\nNext steps:")
    print("1. Open the CSV file to review the results")
    print("2. Verify complex pairs like XXBTZUSD, XETHZEUR")
    print("3. Check that asset conversions are correct")
    print("4. The data is current as of the API call time")
    
    return failed_parses == 0


if __name__ == "__main__":
    success = generate_kraken_pairs_csv()
    
    print(f"\n" + "=" * 80)
    if success:
        print("🎉 CSV generation completed successfully!")
    else:
        print("❌ CSV generation completed with some issues")
    
    print("\nThe generated CSV contains all current Kraken trading pairs")
    print("parsed using live API data for maximum accuracy.")
    print("=" * 80)
    
    sys.exit(0 if success else 1)
