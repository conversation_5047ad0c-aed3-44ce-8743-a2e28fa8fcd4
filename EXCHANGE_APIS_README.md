# Exchange APIs - Minimal Trade Export Library

A lightweight Python library for exporting trade history from cryptocurrency exchanges, extracted from the rotke<PERSON><PERSON> project with minimal dependencies.

## Features

- **Unified Trade Object**: Consistent trade representation across all exchanges
- **Minimal Dependencies**: Only requires `requests` library
- **Multiple Exchanges**: Support for Kraken, Binance, Coinbase, and more
- **Rate Limiting**: Built-in rate limiting to respect exchange API limits
- **Error Handling**: Robust error handling and retry mechanisms
- **Easy Integration**: Simple API for integration into other projects

## Supported Exchanges

- ✅ Kraken
- ✅ Binance
- ✅ Coinbase
- 🚧 More exchanges can be easily added following the same pattern

## Installation

1. Clone or download this library
2. Install dependencies:
```bash
pip install -r exchange_apis_requirements.txt
```

## Quick Start

```python
from exchange_apis.exchanges.kraken import KrakenExchange
from exchange_apis.core.types import Timestamp
from datetime import datetime, timedelta

# Initialize exchange
kraken = KrakenExchange(
    name="my_kraken",
    api_key="your_api_key",
    api_secret=b"your_api_secret",
)

# Validate credentials
is_valid, message = kraken.validate_api_credentials()
if not is_valid:
    print(f"Invalid credentials: {message}")
    exit(1)

# Query trade history for last 30 days
end_time = Timestamp(int(datetime.now().timestamp()))
start_time = Timestamp(int((datetime.now() - timedelta(days=30)).timestamp()))

trades = kraken.query_trade_history(start_time, end_time)

# Process trades
for trade in trades:
    print(f"{trade.trade_type} {trade.amount} {trade.base_asset} "
          f"at {trade.rate} {trade.quote_asset} on {trade.location}")
    
# Export to JSON
import json
trades_data = [trade.to_dict() for trade in trades]
with open('trades.json', 'w') as f:
    json.dump(trades_data, f, indent=2)
```

## Trade Object Structure

Each trade is represented by a `Trade` object with the following fields:

```python
@dataclass
class Trade:
    timestamp: Timestamp          # Unix timestamp
    location: Location           # Exchange (KRAKEN, BINANCE, etc.)
    base_asset: Asset           # Asset being bought/sold
    quote_asset: Asset          # Asset used for payment
    trade_type: TradeType       # BUY or SELL
    amount: AssetAmount         # Amount of base asset
    rate: Price                 # Price per unit
    fee: Optional[Fee]          # Trading fee
    fee_currency: Optional[Asset] # Fee currency
    link: Optional[str]         # Exchange trade ID
    notes: Optional[str]        # Additional notes
```

## Adding New Exchanges

To add support for a new exchange:

1. Create a new file in `exchange_apis/exchanges/`
2. Inherit from `BaseExchange`
3. Implement required methods:
   - `validate_api_credentials()`
   - `query_trade_history()`
   - `_get_auth_headers()`
   - `_parse_trade_data()`

Example:

```python
from .base import BaseExchange

class NewExchange(BaseExchange):
    def __init__(self, name: str, api_key: str, api_secret: bytes):
        super().__init__(
            name=name,
            location=Location.NEW_EXCHANGE,  # Add to Location enum
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,
            base_url="https://api.newexchange.com",
        )
    
    def validate_api_credentials(self):
        # Implement credential validation
        pass
    
    def query_trade_history(self, start_timestamp, end_timestamp):
        # Implement trade history querying
        pass
    
    def _get_auth_headers(self, endpoint, method, params, data):
        # Implement authentication headers
        pass
    
    def _parse_trade_data(self, raw_trade):
        # Implement trade data parsing
        pass
```

## Configuration

### Environment Variables

You can set API credentials using environment variables:

```bash
export KRAKEN_API_KEY="your_kraken_api_key"
export KRAKEN_API_SECRET="your_kraken_api_secret"
export BINANCE_API_KEY="your_binance_api_key"
export BINANCE_API_SECRET="your_binance_api_secret"
export COINBASE_API_KEY="your_coinbase_api_key"
export COINBASE_API_SECRET="your_coinbase_api_secret"
export COINBASE_PASSPHRASE="your_coinbase_passphrase"
```

### Rate Limiting

Each exchange has built-in rate limiting. You can adjust the rate limit when initializing:

```python
exchange = KrakenExchange(
    name="my_kraken",
    api_key="...",
    api_secret=b"...",
    rate_limit=0.5,  # 0.5 calls per second
)
```

## Error Handling

The library includes comprehensive error handling:

- `APIError`: Base exception for API-related errors
- `RateLimitError`: Rate limit exceeded
- `AuthenticationError`: Invalid credentials

```python
from exchange_apis.core.utils import APIError, RateLimitError

try:
    trades = exchange.query_trade_history(start_time, end_time)
except RateLimitError:
    print("Rate limit exceeded, please wait")
except AuthenticationError:
    print("Invalid API credentials")
except APIError as e:
    print(f"API error: {e}")
```

## Differences from Original Rotkehlchen

This library is a simplified extraction focusing only on trade export:

- **Removed**: Database dependencies, complex asset resolution, UI components
- **Simplified**: Asset representation, error handling, configuration
- **Added**: Standalone operation, minimal dependencies, easy integration

## License

This code is extracted and simplified from the rotkehlchen project. Please refer to the original project's license terms.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new exchanges
4. Submit a pull request

## Support

For issues and questions:
1. Check the example usage in `example_usage.py`
2. Review the original rotkehlchen documentation for exchange-specific details
3. Open an issue with detailed error information
