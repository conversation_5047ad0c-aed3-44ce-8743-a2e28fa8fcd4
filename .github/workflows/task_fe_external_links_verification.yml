name: Frontend External Links Verification

on:
  workflow_call:

permissions: { }

jobs:
  unit:
    name: 'verify:link'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    env:
      CYPRESS_INSTALL_BINARY: 0
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          persist-credentials: false

      - name: Load env
        uses: rotki/action-env@v2
        with:
          env_file: .github/.env.ci

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          package_json_file: frontend/package.json

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: 'frontend/.nvmrc'
          cache: 'pnpm'
          cache-dependency-path: 'frontend/pnpm-lock.yaml'

      - name: Install dependencies
        working-directory: ./frontend
        run: pnpm install --frozen-lockfile

      - name: Verify External Links
        working-directory: ./frontend
        run: pnpm run --filter rotki link:verify
