name: <PERSON><PERSON><PERSON> Docker Release
on:
  release:
    types: [ published ]

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref }}
  cancel-in-progress: true

permissions: { }

jobs:
  docker:
    name: Update ${{ github.repository }}:latest on hub.docker.com
    runs-on: ubuntu-latest
    environment: docker
    env:
      REGCTL: v0.8.0
      REGCTL_SUM: 'df25ceaadf190ec73804023657673128a62d909cca12ccd7a9a727a0b6df84b846b00a89ae581bf179528d74dce6a12fb8b7f5e55e546615b75e0a5e5c56c227'
    steps:
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Rotki Version
        id: rotki_version
        run: echo "version=${GITHUB_REF#refs/*/}" >> "$GITHUB_OUTPUT"

      - name: Cache regctl
        uses: actions/cache@v4
        with:
          path: regctl-linux-amd64
          key: ${{ runner.os }}-regctl-${{ env.REGCTL }}

      - name: Publish ${{ github.repository }}:latest from ${{ steps.rotki_version.outputs.version }}
        run: |
          if [[ ! -f "regctl-linux-amd64" ]]; then
            echo "downloading regctl ${REGCTL}"
            curl -LO --fail-with-body "https://github.com/regclient/regclient/releases/download/${REGCTL}/regctl-linux-amd64"            
          fi
          chmod u+x regctl-linux-amd64
          echo "${REGCTL_SUM} regctl-linux-amd64" > regctl-linux-amd64.sha512
          if [[ ! $(sha512sum -c regctl-linux-amd64.sha512) ]]; then 
            echo checksum failed
            exit 1
          fi

      - name: Publish latest to hub.docker.com
        run: ./regctl-linux-amd64 image copy "${REPOSITORY}:${VERSION}" "${REPOSITORY}:latest"
        env:
          REPOSITORY: ${{ github.repository }}
          VERSION: ${{ steps.rotki_version.outputs.version }}
