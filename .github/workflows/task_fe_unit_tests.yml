name: Frontend Unit Tests

on:
  workflow_call:
    secrets:
      CODECOV_TOKEN:
        required: false

permissions: { }

jobs:
  unit:
    name: 'vitest'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          persist-credentials: false

      - name: Load env
        uses: rotki/action-env@v2
        with:
          env_file: .github/.env.ci

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          package_json_file: frontend/package.json

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: 'frontend/.nvmrc'
          cache: 'pnpm'
          cache-dependency-path: 'frontend/pnpm-lock.yaml'

      - name: Install dependencies
        working-directory: ./frontend
        run: pnpm install --frozen-lockfile

      - name: Run unit tests
        working-directory: ./frontend/app
        run: pnpm run --filter rotki test:unit

      - name: Upload coverage
        uses: codecov/codecov-action@v4
        with:
          flags: frontend_unit
          token: ${{ secrets.CODECOV_TOKEN }}