{"name": "@rotki/common", "version": "1.0.0", "description": "Rotki common frontend code", "type": "module", "license": "AGPL-3.0", "homepage": "https://rotki.com", "repository": "https://github.com/rotki/rotki", "author": "Rotki Solutions GmbH <<EMAIL>>", "files": ["lib/"], "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"prepare": "pnpm run build", "build": "tsup --tsconfig tsconfig.lib.json", "watch": "tsup --watch --tsconfig tsconfig.lib.json"}, "peerDependencies": {"@vueuse/core": ">=10.11.0", "bignumber.js": "9.1.2", "vue": ">=3.4.0", "zod": "3.23.8"}, "devDependencies": {"@vueuse/core": "12.7.0", "bignumber.js": "9.1.2", "tsup": "8.4.0", "typescript": "5.6.3", "vue": "3.5.13", "zod": "3.24.2"}}