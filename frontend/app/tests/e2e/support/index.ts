// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import '@cypress/code-coverage/support';
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')
Cypress.on('uncaught:exception', (err) => {
  // returning false here prevents <PERSON><PERSON> from
  // failing the test
  if (!err.message.includes('ResizeObserver loop'))
    console.error(err);

  return false;
});

let hasTestFailed = false;

beforeEach(function () {
  if (hasTestFailed) {
    // Forcefully terminate the current test
    this.skip();
  }
});

afterEach(function () {
  if (this.currentTest?.state === 'failed') {
    hasTestFailed = true;
  }
});
