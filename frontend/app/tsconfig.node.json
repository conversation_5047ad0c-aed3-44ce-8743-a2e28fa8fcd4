{"extends": "@tsconfig/node20/tsconfig.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "noEmit": true}, "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "scripts/**/*", "shared/external-links.ts"]}