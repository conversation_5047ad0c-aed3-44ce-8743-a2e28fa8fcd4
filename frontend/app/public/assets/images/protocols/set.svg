<?xml version="1.0" encoding="UTF-8"?>
<svg width="33px" height="44px" viewBox="0 0 33 44" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54.1 (76490) - https://sketchapp.com -->
    <title>Set Logo Icon</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#3100FF" offset="0%"></stop>
            <stop stop-color="#3100FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Set-Logo-Icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Set-+-TokenSets" fill="url(#linearGradient-1)">
            <g id="Group">
                <g id="Group-20-Copy" transform="translate(0.223589, 0.164535)">
                    <g id="Group-19">
                        <g id="Group-2-Copy" transform="translate(16.566618, 15.200993) rotate(-90.000000) translate(-16.566618, -15.200993) translate(1.566618, 0.700993)">
                            <ellipse id="Oval" cx="14.8327083" cy="14.255783" rx="14.8327083" ry="14.2274455"></ellipse>
                        </g>
                        <g id="Group-2-Copy-2" transform="translate(15.481025, 29.037061) rotate(-270.000000) translate(-15.481025, -29.037061) translate(0.481025, 14.537061)">
                            <ellipse id="Oval" cx="14.8327083" cy="14.255783" rx="14.8327083" ry="14.2274455"></ellipse>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>