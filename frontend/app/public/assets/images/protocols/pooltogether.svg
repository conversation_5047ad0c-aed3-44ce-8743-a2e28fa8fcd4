<svg width="318" height="318" viewBox="0 0 318 318" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<circle cx="159" cy="159" r="159" fill="url(#paint0_linear)"/>
<path d="M166.159 43.5034C202.243 43.5034 231.495 73.2437 231.495 109.93V148.126C231.495 184.812 202.243 214.553 166.159 214.553C160.491 214.553 154.991 213.819 149.747 212.44L149.748 216.949C149.748 243.786 128.246 265.593 101.556 266.027L100.746 266.033V119.894L100.821 119.893L100.823 109.93C100.823 73.2437 130.075 43.5034 166.159 43.5034ZM166.159 93.3236C157.138 93.3236 149.825 100.759 149.825 109.93V148.126C149.825 157.297 157.138 164.732 166.159 164.732C175.18 164.732 182.493 157.297 182.493 148.126V109.93C182.493 100.759 175.18 93.3236 166.159 93.3236Z" fill="white"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="159" y1="0" x2="159" y2="318" gradientUnits="userSpaceOnUse">
<stop stop-color="#7E46F2"/>
<stop offset="1" stop-color="#46279A"/>
</linearGradient>
<clipPath id="clip0">
<rect width="318" height="318" fill="white"/>
</clipPath>
</defs>
</svg>
