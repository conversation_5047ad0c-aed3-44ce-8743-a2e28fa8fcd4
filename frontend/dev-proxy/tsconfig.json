{
  "compilerOptions": {
    // Treat files as modules even if it doesn't use import/export
    "moduleDetection": "force",
    // Ignore module structure
    "module": "Preserve",
    // Allow JSON modules to be imported
    "resolveJsonModule": true,
    // Allow JS files to be imported from TS and vice versa
    "allowJs": true,
    // Use correct ESM import behavior
    "esModuleInterop": true,
    // Disallow features that require cross-file awareness
    "isolatedModules": true
  },
  "include": [
    "src/**/*"
  ]
}
