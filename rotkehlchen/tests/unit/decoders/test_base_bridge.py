import pytest

from rotkehlchen.assets.asset import Asset
from rotkehlchen.chain.evm.decoding.constants import CPT_BASE, CPT_GAS
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.constants.assets import A_ETH
from rotkehlchen.fval import FVal
from rotkehlchen.history.events.structures.evm_event import EvmEvent
from rotkehlchen.history.events.structures.types import HistoryEventSubType, HistoryEventType
from rotkehlchen.tests.utils.ethereum import get_decoded_events_of_transaction
from rotkehlchen.types import Location, TimestampMS, deserialize_evm_tx_hash


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_deposit_eth(ethereum_inquirer, ethereum_accounts):
    evmhash = deserialize_evm_tx_hash('0xbd58a2802a40659da35ff838017a00ba0e251dd0c96ae0c802bd41b5a999f366')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=evmhash)
    user_address = ethereum_accounts[0]
    assert events == [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal('0.**********'),
            location_label=user_address,
            notes='Burn 0.********** ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=1,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.DEPOSIT,
            event_subtype=HistoryEventSubType.BRIDGE,
            asset=A_ETH,
            amount=FVal('200'),
            location_label=user_address,
            notes='Bridge 200 ETH from Ethereum to Base via Base bridge',
            counterparty=CPT_BASE,
            address=string_to_evm_address('******************************************'),
        ),
    ]


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_withdraw_eth(ethereum_inquirer, ethereum_accounts):
    evmhash = deserialize_evm_tx_hash('0x6f62277e5fe0c7d8c613b66b6850dd4b6cf193f830b52486d7d9b79917441e46')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=evmhash)
    user_address = ethereum_accounts[0]
    assert events == [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal('0.****************'),
            location_label=user_address,
            notes='Burn 0.**************** ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=1,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.WITHDRAWAL,
            event_subtype=HistoryEventSubType.BRIDGE,
            asset=A_ETH,
            amount=FVal('0.003'),
            location_label=user_address,
            notes='Bridge 0.003 ETH from Base to Ethereum via Base bridge',
            counterparty=CPT_BASE,
            address=string_to_evm_address('******************************************'),
        ),
    ]


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_deposit_token(ethereum_inquirer, ethereum_accounts):
    evmhash = deserialize_evm_tx_hash('0x9593200706ea6941eac1c8189b9648e9ebab5bd14504c4a493f5309f85e6cba6')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=evmhash)
    user_address = ethereum_accounts[0]
    assert events == [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal('0.*****************'),
            location_label=user_address,
            notes='Burn 0.***************** ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=476,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.DEPOSIT,
            event_subtype=HistoryEventSubType.BRIDGE,
            asset=Asset('eip155:1/erc20:******************************************'),
            amount=FVal('104.9426'),
            location_label=user_address,
            notes='Bridge 104.9426 cbETH from Ethereum to Base via Base bridge',
            counterparty=CPT_BASE,
            address=string_to_evm_address('******************************************'),
        ),
    ]


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_withdraw_token(ethereum_inquirer, ethereum_accounts):
    evmhash = deserialize_evm_tx_hash('0x2d047f0b7a0f2052791359ef82eab317b6d6a685a3c24614f51e8775f4b60ef4')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=evmhash)
    user_address = ethereum_accounts[0]
    assert events == [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal('0.002955477492625515'),
            location_label=user_address,
            notes='Burn 0.002955477492625515 ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=196,
            timestamp=TimestampMS(*************),
            location=Location.ETHEREUM,
            event_type=HistoryEventType.WITHDRAWAL,
            event_subtype=HistoryEventSubType.BRIDGE,
            asset=Asset('eip155:1/erc20:******************************************'),
            amount=FVal('189.09'),
            location_label=user_address,
            notes='Bridge 189.09 cbETH from Base to Ethereum via Base bridge',
            counterparty=CPT_BASE,
            address=string_to_evm_address('******************************************'),
        ),
    ]
