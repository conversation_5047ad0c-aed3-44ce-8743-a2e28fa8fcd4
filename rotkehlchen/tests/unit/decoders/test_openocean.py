from typing import TYPE_CHECKING

import pytest

from rotkehlchen.assets.asset import Asset
from rotkehlchen.chain.evm.decoding.constants import CPT_GAS
from rotkehlchen.chain.evm.decoding.open_ocean.constants import (
    CPT_OPENOCEAN,
    OPENOCEAN_EXCHANGE_ADDRESS,
    OPENOCEAN_LABEL,
)
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.constants.assets import (
    A_ARB,
    A_BSC_BNB,
    A_ETH,
    A_POLYGON_POS_MATIC,
    A_WETH,
    A_XDAI,
)
from rotkehlchen.fval import FVal
from rotkehlchen.history.events.structures.evm_event import EvmEvent
from rotkehlchen.history.events.structures.types import HistoryEventSubType, HistoryEventType
from rotkehlchen.tests.utils.ethereum import get_decoded_events_of_transaction
from rotkehlchen.types import Location, TimestampMS, deserialize_evm_tx_hash

if TYPE_CHECKING:
    from rotkehlchen.chain.arbitrum_one.node_inquirer import ArbitrumOneInquirer
    from rotkehlchen.chain.base.node_inquirer import BaseInquirer
    from rotkehlchen.chain.binance_sc.node_inquirer import BinanceSCInquirer
    from rotkehlchen.chain.ethereum.node_inquirer import EthereumInquirer
    from rotkehlchen.chain.gnosis.node_inquirer import GnosisInquirer
    from rotkehlchen.chain.optimism.node_inquirer import OptimismInquirer
    from rotkehlchen.chain.polygon_pos.node_inquirer import PolygonPOSInquirer
    from rotkehlchen.types import ChecksumEvmAddress


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('polygon_pos_accounts', [['******************************************']])
def test_openocean_swap_token_to_token(
        polygon_pos_inquirer: 'PolygonPOSInquirer',
        polygon_pos_accounts: list['ChecksumEvmAddress'],
):
    tx_hash = deserialize_evm_tx_hash('0x4c335999c657e3d6f4dfe875d50911a844580c42a667d6952211459c0eae587d')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=polygon_pos_inquirer, tx_hash=tx_hash)  # noqa: E501
    user_address, timestamp, gas_amount, spend_amount, receive_amount, approve_amount = polygon_pos_accounts[0], TimestampMS(*************), '0.245916524062468656', '10000', '433.966123379781075037', '115792089237316195423570985008687907853269984665640564039457584007893003.638699'  # noqa: E501
    pos_usdt = Asset('eip155:137/erc20:0xc2132D05D31c914a87C6611C10748AEb04B58e8F')
    assert events == [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.POLYGON_POS,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_POLYGON_POS_MATIC,
            amount=FVal(gas_amount),
            location_label=user_address,
            notes=f'Burn {gas_amount} POL for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=14,
            timestamp=timestamp,
            location=Location.POLYGON_POS,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.APPROVE,
            asset=pos_usdt,
            amount=FVal(approve_amount),
            location_label=user_address,
            notes=f'Set USDT spending approval of {user_address} by ****************************************** to {approve_amount}',  # noqa: E501
            address=string_to_evm_address('******************************************'),
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=15,
            timestamp=timestamp,
            location=Location.POLYGON_POS,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            asset=pos_usdt,
            amount=FVal(spend_amount),
            location_label=user_address,
            notes=f'Swap {spend_amount} USDT in {OPENOCEAN_LABEL}',
            counterparty=CPT_OPENOCEAN,
            address=string_to_evm_address('******************************************'),
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=16,
            timestamp=timestamp,
            location=Location.POLYGON_POS,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            asset=Asset('eip155:137/erc20:0x53E0bca35eC356BD5ddDFebbD1Fc0fD03FaBad39'),
            amount=FVal(receive_amount),
            location_label=user_address,
            notes=f'Receive {receive_amount} LINK from {OPENOCEAN_LABEL} swap',
            counterparty=CPT_OPENOCEAN,
            address=string_to_evm_address('******************************************'),
        ),
    ]


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('base_accounts', [['******************************************']])
def test_openocean_swap_eth_to_token(
        base_inquirer: 'BaseInquirer',
        base_accounts: list['ChecksumEvmAddress'],
):
    tx_hash = deserialize_evm_tx_hash('0xe45c138f3086f821c6598b24999c9cc0ca9ed95ecccc08b57f751d7146fd01ca')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=base_inquirer, tx_hash=tx_hash)
    user_address, timestamp, gas_amount, spend_amount, receive_amount = base_accounts[0], TimestampMS(*************), '0.000005051509746809', '0.0031', '1013.977585253488605008'  # noqa: E501
    assert events == [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.BASE,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount),
            location_label=user_address,
            notes=f'Burn {gas_amount} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=1,
            timestamp=timestamp,
            location=Location.BASE,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            asset=A_ETH,
            amount=FVal(spend_amount),
            location_label=user_address,
            notes=f'Swap {spend_amount} ETH in {OPENOCEAN_LABEL}',
            counterparty=CPT_OPENOCEAN,
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=2,
            timestamp=timestamp,
            location=Location.BASE,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            asset=Asset('eip155:8453/erc20:******************************************'),
            amount=FVal(receive_amount),
            location_label=user_address,
            notes=f'Receive {receive_amount} cbDXN from {OPENOCEAN_LABEL} swap',
            counterparty=CPT_OPENOCEAN,
            address=string_to_evm_address('******************************************'),
        ),
    ]


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('optimism_accounts', [['******************************************']])
def test_openocean_swap_token_to_eth(
        optimism_inquirer: 'OptimismInquirer',
        optimism_accounts: list['ChecksumEvmAddress'],
):
    tx_hash = deserialize_evm_tx_hash('0x076fdb84e51d17d2f9727c34f4b73cc1ffcd3671165764a0a287fe09a0cab36e')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=optimism_inquirer, tx_hash=tx_hash)
    user_address, timestamp, gas_amount, spend_amount, receive_amount, approve_amount = optimism_accounts[0], TimestampMS(*************), '0.000002907634681445', '5', '0.001484145603280848', '115792089237316195423570985008687907853269984665640564039457584007913121.115067'  # noqa: E501
    op_usdce = Asset('eip155:10/erc20:******************************************')
    assert events == [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.OPTIMISM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount),
            location_label=user_address,
            notes=f'Burn {gas_amount} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=2,
            timestamp=timestamp,
            location=Location.OPTIMISM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.APPROVE,
            asset=op_usdce,
            amount=FVal(approve_amount),
            location_label=user_address,
            notes=f'Set USDC.e spending approval of {user_address} by ****************************************** to {approve_amount}',  # noqa: E501
            address=string_to_evm_address('******************************************'),
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=3,
            timestamp=timestamp,
            location=Location.OPTIMISM,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            asset=op_usdce,
            amount=FVal(spend_amount),
            location_label=user_address,
            notes=f'Swap {spend_amount} USDC.e in {OPENOCEAN_LABEL}',
            counterparty=CPT_OPENOCEAN,
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=4,
            timestamp=timestamp,
            location=Location.OPTIMISM,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            asset=A_ETH,
            amount=FVal(receive_amount),
            location_label=user_address,
            notes=f'Receive {receive_amount} ETH from {OPENOCEAN_LABEL} swap',
            counterparty=CPT_OPENOCEAN,
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ),
    ]


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('arbitrum_one_accounts', [['******************************************']])
def test_openocean_swap_uniswap_with_swapped_log(
        arbitrum_one_inquirer: 'ArbitrumOneInquirer',
        arbitrum_one_accounts: list['ChecksumEvmAddress'],
):
    tx_hash = deserialize_evm_tx_hash('0xf882a6a323bdc7ac05e78e2c346fddb489249afc04e68f09b1858ab357c8ede0')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=arbitrum_one_inquirer, tx_hash=tx_hash)  # noqa: E501
    user_address, timestamp, gas_amount, spend_amount, receive_amount, approve_amount = arbitrum_one_accounts[0], TimestampMS(*************), '0.**********', '3000', '3412.773848', '99999999999999999999996999'  # noqa: E501
    assert events == [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ARBITRUM_ONE,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount),
            location_label=user_address,
            notes=f'Burn {gas_amount} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=1,
            timestamp=timestamp,
            location=Location.ARBITRUM_ONE,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.APPROVE,
            asset=A_ARB,
            amount=FVal(approve_amount),
            location_label=user_address,
            notes=f'Set ARB spending approval of {user_address} by {OPENOCEAN_EXCHANGE_ADDRESS} to {approve_amount}',  # noqa: E501
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=2,
            timestamp=timestamp,
            location=Location.ARBITRUM_ONE,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            asset=A_ARB,
            amount=FVal(spend_amount),
            location_label=user_address,
            notes=f'Swap {spend_amount} ARB in {OPENOCEAN_LABEL}',
            counterparty=CPT_OPENOCEAN,
            address=string_to_evm_address('******************************************'),
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=3,
            timestamp=timestamp,
            location=Location.ARBITRUM_ONE,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            asset=Asset('eip155:42161/erc20:******************************************'),
            amount=FVal(receive_amount),
            location_label=user_address,
            notes=f'Receive {receive_amount} USDC from {OPENOCEAN_LABEL} swap',
            counterparty=CPT_OPENOCEAN,
            address=string_to_evm_address('******************************************'),
        ),
    ]


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_openocean_swap_uniswapv2(
        ethereum_inquirer: 'EthereumInquirer',
        ethereum_accounts: list['ChecksumEvmAddress'],
):
    tx_hash = deserialize_evm_tx_hash('0x497acb2385e7d7bc39160097348bd6423ecfe50f616dd2841cecd25a6561fae2')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    user_address, timestamp, gas_amount, spend_amount, receive_amount = ethereum_accounts[0], TimestampMS(*************), '0.*****************', '7500', '0.*****************'  # noqa: E501
    assert events == [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount),
            location_label=user_address,
            notes=f'Burn {gas_amount} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=1,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            asset=Asset('eip155:1/erc20:******************************************'),
            amount=FVal(spend_amount),
            location_label=user_address,
            notes=f'Swap {spend_amount} YOURAI in {OPENOCEAN_LABEL}',
            counterparty=CPT_OPENOCEAN,
            address=string_to_evm_address('******************************************'),
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=2,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            asset=A_WETH,
            amount=FVal(receive_amount),
            location_label=user_address,
            notes=f'Receive {receive_amount} WETH from {OPENOCEAN_LABEL} swap',
            counterparty=CPT_OPENOCEAN,
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ),
    ]


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_openocean_swap_uniswapv3(
        ethereum_inquirer: 'EthereumInquirer',
        ethereum_accounts: list['ChecksumEvmAddress'],
):
    tx_hash = deserialize_evm_tx_hash('0xb8caa647073e36ea91c2c22e57b49ddd1721c6c8b18ef51c3043adaa2950251b')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    user_address, timestamp, gas_amount, spend_amount, receive_amount = ethereum_accounts[0], TimestampMS(*************), '0.****************', '0.02515', '800.247565502385530038'  # noqa: E501
    assert events == [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount),
            location_label=user_address,
            notes=f'Burn {gas_amount} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=1,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            asset=A_ETH,
            amount=FVal(spend_amount),
            location_label=user_address,
            notes=f'Swap {spend_amount} ETH in {OPENOCEAN_LABEL}',
            counterparty=CPT_OPENOCEAN,
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=2,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            asset=Asset('eip155:1/erc20:******************************************'),
            amount=FVal(receive_amount),
            location_label=user_address,
            notes=f'Receive {receive_amount} ISLAND from {OPENOCEAN_LABEL} swap',
            counterparty=CPT_OPENOCEAN,
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ),
    ]


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('binance_sc_accounts', [['******************************************']])
def test_openocean_swap_on_binance_sc(
        binance_sc_inquirer: 'BinanceSCInquirer',
        binance_sc_accounts: list['ChecksumEvmAddress'],
) -> None:
    tx_hash = deserialize_evm_tx_hash('0x401b2209ef972bf9f3e77b743df65b9a0057e21e3a325b790811902d6faa6f2f')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=binance_sc_inquirer, tx_hash=tx_hash)  # noqa: E501
    user_address, timestamp, gas_amount, spend_amount, receive_amount, approve_amount = binance_sc_accounts[0], TimestampMS(*************), '0.*********', '0.006797769273691619', '0.031725949469604878', '115792089237316195423570985008687907853269984665640564039457.577210143855948316'  # noqa: E501
    a_bsc_eth = Asset('eip155:56/erc20:******************************************')
    assert events == [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.BINANCE_SC,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_BSC_BNB,
        amount=FVal(gas_amount),
        location_label=user_address,
        notes=f'Burn {gas_amount} BNB for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=338,
        timestamp=timestamp,
        location=Location.BINANCE_SC,
        event_type=HistoryEventType.INFORMATIONAL,
        event_subtype=HistoryEventSubType.APPROVE,
        asset=a_bsc_eth,
        amount=FVal(approve_amount),
        location_label=user_address,
        notes=f'Set ETH spending approval of {user_address} by ****************************************** to {approve_amount}',  # noqa: E501
        address=string_to_evm_address('******************************************'),
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=339,
        timestamp=timestamp,
        location=Location.BINANCE_SC,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=a_bsc_eth,
        amount=FVal(spend_amount),
        location_label=user_address,
        notes=f'Swap {spend_amount} ETH in {OPENOCEAN_LABEL}',
        counterparty=CPT_OPENOCEAN,
        address=string_to_evm_address('******************************************'),
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=340,
        timestamp=timestamp,
        location=Location.BINANCE_SC,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_BSC_BNB,
        amount=FVal(receive_amount),
        location_label=user_address,
        notes=f'Receive {receive_amount} BNB from {OPENOCEAN_LABEL} swap',
        counterparty=CPT_OPENOCEAN,
        address=string_to_evm_address('******************************************'),
    )]


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('gnosis_accounts', [['******************************************']])
def test_openocean_swap_xdai_to_token(
        gnosis_inquirer: 'GnosisInquirer',
        gnosis_accounts: list['ChecksumEvmAddress'],
):
    tx_hash = deserialize_evm_tx_hash('0x806a840fd2c7ed43eefb2069a3a1d1921b668f3762a3ec6928549055d5b65453')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=gnosis_inquirer, tx_hash=tx_hash)
    user_address, timestamp, gas_amount, spend_amount, receive_amount = gnosis_accounts[0], TimestampMS(*************), '0.*********', '18.7', '18.651429'  # noqa: E501
    assert events == [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.GNOSIS,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_XDAI,
            amount=FVal(gas_amount),
            location_label=user_address,
            notes=f'Burn {gas_amount} XDAI for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=1,
            timestamp=timestamp,
            location=Location.GNOSIS,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            asset=A_XDAI,
            amount=FVal(spend_amount),
            location_label=user_address,
            notes=f'Swap {spend_amount} XDAI in {OPENOCEAN_LABEL}',
            counterparty=CPT_OPENOCEAN,
            address=OPENOCEAN_EXCHANGE_ADDRESS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=2,
            timestamp=timestamp,
            location=Location.GNOSIS,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            asset=Asset('eip155:100/erc20:0xDDAfbb505ad214D7b80b1f830fcCc89B60fb7A83'),
            amount=FVal(receive_amount),
            location_label=user_address,
            notes=f'Receive {receive_amount} USDC from {OPENOCEAN_LABEL} swap',
            counterparty=CPT_OPENOCEAN,
            address=string_to_evm_address('0x8D2B7e5501Eb6D92F8e349f2FEbe785DD070bE74'),
        ),
    ]
