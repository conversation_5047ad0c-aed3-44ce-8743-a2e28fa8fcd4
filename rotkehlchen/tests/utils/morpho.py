from typing import TYPE_CHECKING

from rotkehlchen.assets.asset import UnderlyingToken
from rotkehlchen.assets.utils import get_or_create_evm_token
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.constants import ONE
from rotkehlchen.types import MORPHO_VAULT_PROTOCOL, ChainID, EvmTokenKind

if TYPE_CHECKING:
    from rotkehlchen.assets.asset import EvmToken
    from rotkehlchen.db.d<PERSON>ndler import <PERSON><PERSON><PERSON>ler


def create_base_morpho_vault_token(database: 'DBHandler') -> 'EvmToken':
    """Ensure vault token for Base tests is in the database for proper decoding.
    Returns the vault token.
    """
    return get_or_create_evm_token(
        userdb=database,
        evm_address=string_to_evm_address('0xc1256Ae5FF1cf2719D4937adb3bbCCab2E00A2Ca'),
        chain_id=ChainID.BASE,
        token_kind=EvmTokenKind.ERC20,
        symbol='mwUSDC',
        name='Moonwell Flagship USDC',
        decimals=18,
        protocol=MORPHO_VAULT_PROTOCOL,
        underlying_tokens=[UnderlyingToken(
            address=string_to_evm_address('0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913'),
            token_kind=EvmTokenKind.ERC20,
            weight=ONE,
        )],
    )


def create_base_morpho_vault_tokens_for_bundler_test(
        database: 'DBHandler',
) -> tuple['EvmToken', 'EvmToken']:
    """Ensure vault tokens for the bundler test are in the database for proper decoding.
    Returns a tuple containing the vault tokens.
    """
    re7_token = get_or_create_evm_token(
        userdb=database,
        evm_address=string_to_evm_address('******************************************'),
        chain_id=ChainID.BASE,
        token_kind=EvmTokenKind.ERC20,
        symbol='Re7WETH',
        name='Re7 WETH',
        decimals=18,
        protocol=MORPHO_VAULT_PROTOCOL,
        underlying_tokens=[UnderlyingToken(
            address=string_to_evm_address('******************************************'),
            token_kind=EvmTokenKind.ERC20,
            weight=ONE,
        )],
    )
    pyth_token = get_or_create_evm_token(
        userdb=database,
        evm_address=string_to_evm_address('******************************************'),
        chain_id=ChainID.BASE,
        token_kind=EvmTokenKind.ERC20,
        symbol='pythETH',
        name='Pyth ETH',
        decimals=18,
        protocol=MORPHO_VAULT_PROTOCOL,
        underlying_tokens=[UnderlyingToken(
            address=string_to_evm_address('******************************************'),
            token_kind=EvmTokenKind.ERC20,
            weight=ONE,
        )],
    )
    return re7_token, pyth_token


def create_base_morpho_ionic_weth_vault_token(database: 'DBHandler') -> 'EvmToken':
    """Ensure the ionicWETH vault token is in the database for proper decoding.
    Returns the vault token.
    """
    return get_or_create_evm_token(
        userdb=database,
        evm_address=string_to_evm_address('******************************************'),
        chain_id=ChainID.BASE,
        symbol='ionicWETH',
        name='Ionic Ecosystem WETH',
        decimals=18,
        protocol=MORPHO_VAULT_PROTOCOL,
        underlying_tokens=[UnderlyingToken(
            address=string_to_evm_address('******************************************'),
            token_kind=EvmTokenKind.ERC20,
            weight=ONE,
        )],
    )


def create_ethereum_morpho_vault_token(database: 'DBHandler') -> 'EvmToken':
    """Ensure vault token for Ethereum tests is in the database for proper decoding.
    Returns the vault token.
    """
    return get_or_create_evm_token(
        userdb=database,
        evm_address=string_to_evm_address('******************************************'),
        chain_id=ChainID.ETHEREUM,
        token_kind=EvmTokenKind.ERC20,
        symbol='USUALUSDC+',
        name='Usual Boosted USDC',
        decimals=18,
        protocol=MORPHO_VAULT_PROTOCOL,
        underlying_tokens=[UnderlyingToken(
            address=string_to_evm_address('******************************************'),
            token_kind=EvmTokenKind.ERC20,
            weight=ONE,
        )],
    )
