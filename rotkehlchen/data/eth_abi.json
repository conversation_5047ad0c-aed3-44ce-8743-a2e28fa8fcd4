{"ZERION_ADAPTER": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [{"internalType": "string", "name": "protocolName", "type": "string"}, {"internalType": "address[]", "name": "adapters", "type": "address[]"}, {"internalType": "address[][]", "name": "tokens", "type": "address[][]"}], "name": "addProtocolAdapters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "protocolNames", "type": "string[]"}, {"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "websiteURL", "type": "string"}, {"internalType": "string", "name": "iconURL", "type": "string"}, {"internalType": "uint256", "name": "version", "type": "uint256"}], "internalType": "struct ProtocolMetadata[]", "name": "metadata", "type": "tuple[]"}, {"internalType": "address[][]", "name": "adapters", "type": "address[][]"}, {"internalType": "address[][][]", "name": "tokens", "type": "address[][][]"}], "name": "addProtocols", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "tokenAdapterNames", "type": "string[]"}, {"internalType": "address[]", "name": "adapters", "type": "address[]"}], "name": "addTokenAdapters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "adapter", "type": "address"}, {"internalType": "address[]", "name": "tokens", "type": "address[]"}], "name": "getAdapterBalance", "outputs": [{"components": [{"components": [{"internalType": "address", "name": "adapter<PERSON>ddress", "type": "address"}, {"internalType": "string", "name": "adapterType", "type": "string"}], "internalType": "struct AdapterMetadata", "name": "metadata", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance", "name": "base", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance[]", "name": "underlying", "type": "tuple[]"}], "internalType": "struct FullTokenBalance[]", "name": "balances", "type": "tuple[]"}], "internalType": "struct AdapterBalance", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address[]", "name": "adapters", "type": "address[]"}], "name": "getAdapterBalances", "outputs": [{"components": [{"components": [{"internalType": "address", "name": "adapter<PERSON>ddress", "type": "address"}, {"internalType": "string", "name": "adapterType", "type": "string"}], "internalType": "struct AdapterMetadata", "name": "metadata", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance", "name": "base", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance[]", "name": "underlying", "type": "tuple[]"}], "internalType": "struct FullTokenBalance[]", "name": "balances", "type": "tuple[]"}], "internalType": "struct AdapterBalance[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getBalances", "outputs": [{"components": [{"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "websiteURL", "type": "string"}, {"internalType": "string", "name": "iconURL", "type": "string"}, {"internalType": "uint256", "name": "version", "type": "uint256"}], "internalType": "struct ProtocolMetadata", "name": "metadata", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "adapter<PERSON>ddress", "type": "address"}, {"internalType": "string", "name": "adapterType", "type": "string"}], "internalType": "struct AdapterMetadata", "name": "metadata", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance", "name": "base", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance[]", "name": "underlying", "type": "tuple[]"}], "internalType": "struct FullTokenBalance[]", "name": "balances", "type": "tuple[]"}], "internalType": "struct AdapterBalance[]", "name": "adapterBalances", "type": "tuple[]"}], "internalType": "struct ProtocolBalance[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "tokenType", "type": "string"}, {"internalType": "address", "name": "token", "type": "address"}], "name": "getFinalFullTokenBalance", "outputs": [{"components": [{"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance", "name": "base", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance[]", "name": "underlying", "type": "tuple[]"}], "internalType": "struct FullTokenBalance", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "tokenType", "type": "string"}, {"internalType": "address", "name": "token", "type": "address"}], "name": "getFullTokenBalance", "outputs": [{"components": [{"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance", "name": "base", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance[]", "name": "underlying", "type": "tuple[]"}], "internalType": "struct FullTokenBalance", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "protocolName", "type": "string"}], "name": "getProtocolAdapters", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "string[]", "name": "protocolNames", "type": "string[]"}], "name": "getProtocolBalances", "outputs": [{"components": [{"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "websiteURL", "type": "string"}, {"internalType": "string", "name": "iconURL", "type": "string"}, {"internalType": "uint256", "name": "version", "type": "uint256"}], "internalType": "struct ProtocolMetadata", "name": "metadata", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "adapter<PERSON>ddress", "type": "address"}, {"internalType": "string", "name": "adapterType", "type": "string"}], "internalType": "struct AdapterMetadata", "name": "metadata", "type": "tuple"}, {"components": [{"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance", "name": "base", "type": "tuple"}, {"components": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct TokenMetadata", "name": "metadata", "type": "tuple"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct TokenBalance[]", "name": "underlying", "type": "tuple[]"}], "internalType": "struct FullTokenBalance[]", "name": "balances", "type": "tuple[]"}], "internalType": "struct AdapterBalance[]", "name": "adapterBalances", "type": "tuple[]"}], "internalType": "struct ProtocolBalance[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "protocolName", "type": "string"}], "name": "getProtocolMetadata", "outputs": [{"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "websiteURL", "type": "string"}, {"internalType": "string", "name": "iconURL", "type": "string"}, {"internalType": "uint256", "name": "version", "type": "uint256"}], "internalType": "struct ProtocolMetadata", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getProtocolNames", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "adapter", "type": "address"}], "name": "getSupportedTokens", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "tokenAdapterName", "type": "string"}], "name": "getTokenAdapter", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTokenAdapterNames", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "protocolName", "type": "string"}], "name": "isValidProtocol", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "tokenAdapterName", "type": "string"}], "name": "isValidTokenAdapter", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "protocolName", "type": "string"}, {"internalType": "uint256[]", "name": "adapterIndices", "type": "uint256[]"}], "name": "removeProtocolAdapters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "protocolNames", "type": "string[]"}], "name": "removeProtocols", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "tokenAdapterNames", "type": "string[]"}], "name": "removeTokenAdapters", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "protocolName", "type": "string"}, {"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "address", "name": "newAdapter<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address[]", "name": "newSupportedTokens", "type": "address[]"}], "name": "updateProtocolAdapter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "protocolName", "type": "string"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "websiteURL", "type": "string"}, {"internalType": "string", "name": "iconURL", "type": "string"}], "name": "updateProtocolMetadata", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "tokenAdapterName", "type": "string"}, {"internalType": "address", "name": "adapter", "type": "address"}], "name": "updateTokenAdapter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "ATOKEN": [{"inputs": [{"internalType": "contract LendingPoolAddressesProvider", "name": "_addressesProvider", "type": "address"}, {"internalType": "address", "name": "_underlyingAsset", "type": "address"}, {"internalType": "uint8", "name": "_underlyingAssetDecimals", "type": "uint8"}, {"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromBalanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_toBalanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_toIndex", "type": "uint256"}], "name": "BalanceTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromBalanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromIndex", "type": "uint256"}], "name": "BurnOnLiquidation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_to", "type": "address"}], "name": "InterestRedirectionAllowanceChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_redirectedBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromBalanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromIndex", "type": "uint256"}], "name": "InterestStreamRedirected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromBalanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromIndex", "type": "uint256"}], "name": "MintOnDeposit", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromBalanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_fromIndex", "type": "uint256"}], "name": "Redeem", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_targetAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_targetBalanceIncrease", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_targetIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_redirectedBalanceAdded", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_redirectedBalanceRemoved", "type": "uint256"}], "name": "RedirectedBalanceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"constant": true, "inputs": [], "name": "UINT_MAX_VALUE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_to", "type": "address"}], "name": "allowInterestRedirectionTo", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_account", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "burnOnLiquidation", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getInterestRedirectionAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getRedirectedBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "isTransferAllowed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_account", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "mintOnDeposit", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "principalBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "redeem", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_to", "type": "address"}], "name": "redirectInterestStream", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}], "name": "redirectInterestStreamOf", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "transferOnLiquidation", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "underlyingAssetAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}], "ERC20_TOKEN": [{"constant": true, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "payable": false, "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "payable": false, "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint8"}], "payable": false, "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "type": "function"}, {"constant": true, "inputs": [{"name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "balance", "type": "uint256"}], "payable": false, "type": "function"}, {"constant": false, "inputs": [{"name": "to", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"name": "success", "type": "bool"}], "payable": false, "type": "function"}, {"constant": false, "inputs": [{"name": "from", "type": "address"}, {"name": "to", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"name": "success", "type": "bool"}], "payable": false, "type": "function"}, {"constant": false, "inputs": [{"name": "spender", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"name": "success", "type": "bool"}], "payable": false, "type": "function"}, {"constant": true, "inputs": [{"name": "owner", "type": "address"}, {"name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"name": "remaining", "type": "uint256"}], "payable": false, "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": true, "name": "to", "type": "address"}, {"indexed": false, "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "owner", "type": "address"}, {"indexed": true, "name": "spender", "type": "address"}, {"indexed": false, "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"inputs": [{"name": "initialAmount", "type": "uint256"}, {"name": "tokenName", "type": "string"}, {"name": "decimalUnits", "type": "uint8"}, {"name": "tokenSymbol", "type": "string"}], "payable": false, "type": "constructor"}, {"constant": false, "inputs": [{"name": "spender", "type": "address"}, {"name": "value", "type": "uint256"}, {"name": "extraData", "type": "bytes"}], "name": "approveAndCall", "outputs": [{"name": "success", "type": "bool"}], "payable": false, "type": "function"}, {"constant": true, "inputs": [], "name": "version", "outputs": [{"name": "", "type": "string"}], "payable": false, "type": "function"}], "CTOKEN": [{"inputs": [{"internalType": "address", "name": "underlying_", "type": "address"}, {"internalType": "contract ComptrollerInterface", "name": "comptroller_", "type": "address"}, {"internalType": "contract InterestRateModel", "name": "interestRateModel_", "type": "address"}, {"internalType": "uint256", "name": "initialExchangeRateMantissa_", "type": "uint256"}, {"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "address payable", "name": "admin_", "type": "address"}, {"internalType": "address", "name": "implementation_", "type": "address"}, {"internalType": "bytes", "name": "becomeImplementationData", "type": "bytes"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "cashPrior", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "interestAccumulated", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "borrowIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalBorrows", "type": "uint256"}], "name": "AccrueInterest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "borrower", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "borrowAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "accountBorrows", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalBorrows", "type": "uint256"}], "name": "Borrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "error", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "info", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "detail", "type": "uint256"}], "name": "Failure", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "liquidator", "type": "address"}, {"indexed": false, "internalType": "address", "name": "borrower", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "cTokenCollateral", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "seizeTokens", "type": "uint256"}], "name": "LiquidateBorrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "minter", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "mintAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "mintTokens", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldAd<PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "NewAdmin", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "contract ComptrollerInterface", "name": "oldComptroller", "type": "address"}, {"indexed": false, "internalType": "contract ComptrollerInterface", "name": "newComptroller", "type": "address"}], "name": "NewComptroller", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldImplementation", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newImplementation", "type": "address"}], "name": "NewImplementation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "contract InterestRateModel", "name": "oldInterestRateModel", "type": "address"}, {"indexed": false, "internalType": "contract InterestRateModel", "name": "newInterestRateModel", "type": "address"}], "name": "NewMarketInterestRateModel", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldPendingAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newPendingAdmin", "type": "address"}], "name": "NewPendingAdmin", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldReserveFactorMantissa", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newReserveFactorMantissa", "type": "uint256"}], "name": "NewReserveFactor", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "redeemer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "redeemAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "name": "Redeem", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "payer", "type": "address"}, {"indexed": false, "internalType": "address", "name": "borrower", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "accountBorrows", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalBorrows", "type": "uint256"}], "name": "RepayBorrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "benefactor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "addAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newTotalReserves", "type": "uint256"}], "name": "ReservesAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "admin", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reduceAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newTotalReserves", "type": "uint256"}], "name": "ReservesReduced", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"payable": true, "stateMutability": "payable", "type": "fallback"}, {"constant": false, "inputs": [], "name": "_acceptAdmin", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "addAmount", "type": "uint256"}], "name": "_addReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "reduceAmount", "type": "uint256"}], "name": "_reduceReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "contract ComptrollerInterface", "name": "newComptroller", "type": "address"}], "name": "_setComptroller", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "implementation_", "type": "address"}, {"internalType": "bool", "name": "allowResign", "type": "bool"}, {"internalType": "bytes", "name": "becomeImplementationData", "type": "bytes"}], "name": "_setImplementation", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "contract InterestRateModel", "name": "newInterestRateModel", "type": "address"}], "name": "_setInterestRateModel", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address payable", "name": "newPendingAdmin", "type": "address"}], "name": "_setPendingAdmin", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "newReserveFactorMantissa", "type": "uint256"}], "name": "_setReserveFactor", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "accrualBlockNumber", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "accrueInterest", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "admin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOfUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}], "name": "borrow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "borrowBalanceCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "borrowBalanceStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "borrowIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "borrowRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "comptroller", "outputs": [{"internalType": "contract ComptrollerInterface", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "delegateToImplementation", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "delegateToViewImplementation", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "exchangeRateCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "exchangeRateStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAccountSnapshot", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getCash", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "interestRateModel", "outputs": [{"internalType": "contract InterestRateModel", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isCToken", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}, {"internalType": "contract CTokenInterface", "name": "cTokenCollateral", "type": "address"}], "name": "liquidateBorrow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "mintAmount", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "pendingAdmin", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "redeemTokens", "type": "uint256"}], "name": "redeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "redeemAmount", "type": "uint256"}], "name": "redeemUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "name": "repayBorrow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "repayAmount", "type": "uint256"}], "name": "repayBorrowBehalf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "reserveFactorMantissa", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "seizeTokens", "type": "uint256"}], "name": "seize", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "supplyRatePerBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "totalBorrows", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "totalBorrowsCurrent", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "totalReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}], "FARM_ASSET": [{"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beneficiary", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Invest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newStrategy", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "time", "type": "uint256"}], "name": "StrategyAnnounced", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newStrategy", "type": "address"}, {"indexed": false, "internalType": "address", "name": "oldStrategy", "type": "address"}], "name": "StrategyChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beneficiary", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdraw", "type": "event"}, {"constant": true, "inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_strategy", "type": "address"}], "name": "announceStrategyUpdate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "availableToInvestOut", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_strategy", "type": "address"}], "name": "canUpdateStrategy", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "controller", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "deposit", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "holder", "type": "address"}], "name": "depositFor", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "doHardWork", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "finalizeStrategyUpdate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "finalizeUpgrade", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "futureStrategy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getPricePerFullShare", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "governance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_underlying", "type": "address"}, {"internalType": "uint256", "name": "_toInvestNumerator", "type": "uint256"}, {"internalType": "uint256", "name": "_toInvestDenominator", "type": "uint256"}, {"internalType": "uint256", "name": "_underlyingUnit", "type": "uint256"}, {"internalType": "uint256", "name": "_implementationChangeDelay", "type": "uint256"}, {"internalType": "uint256", "name": "_strategyChangeDelay", "type": "uint256"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_storage", "type": "address"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_storage", "type": "address"}, {"internalType": "address", "name": "_underlying", "type": "address"}, {"internalType": "uint256", "name": "_toInvestNumerator", "type": "uint256"}, {"internalType": "uint256", "name": "_toInvestDenominator", "type": "uint256"}], "name": "initializeVault", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "nextImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "nextImplementationDelay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "nextImplementationTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "rebalance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "impl", "type": "address"}], "name": "scheduleUpgrade", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_store", "type": "address"}], "name": "setStorage", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_strategy", "type": "address"}], "name": "setStrategy", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "numerator", "type": "uint256"}, {"internalType": "uint256", "name": "denominator", "type": "uint256"}], "name": "setVaultFractionToInvest", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "shouldUpgrade", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "strategy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "strategyTimeLock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "strategyUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "underlyingBalanceInVault", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "underlyingBalanceWithInvestment", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "holder", "type": "address"}], "name": "underlyingBalanceWithInvestmentForHolder", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "underlyingUnit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "vaultFractionToInvestDenominator", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "vaultFractionToInvestNumerator", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "numberOfShares", "type": "uint256"}], "name": "withdraw", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "withdrawAll", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "UNISWAP_V2_LP": [{"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "Burn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount0In", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1In", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount0Out", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1Out", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint112", "name": "reserve0", "type": "uint112"}, {"indexed": false, "internalType": "uint112", "name": "reserve1", "type": "uint112"}], "name": "Sync", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"constant": true, "inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "MINIMUM_LIQUIDITY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "PERMIT_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "burn", "outputs": [{"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "factory", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getReserves", "outputs": [{"internalType": "uint112", "name": "_reserve0", "type": "uint112"}, {"internalType": "uint112", "name": "_reserve1", "type": "uint112"}, {"internalType": "uint32", "name": "_blockTimestampLast", "type": "uint32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_token0", "type": "address"}, {"internalType": "address", "name": "_token1", "type": "address"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "kLast", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "liquidity", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "price0CumulativeLast", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "price1CumulativeLast", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "to", "type": "address"}], "name": "skim", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "amount0Out", "type": "uint256"}, {"internalType": "uint256", "name": "amount1Out", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "swap", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "sync", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "token0", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "token1", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "CURVE_POOL": [{"name": "TokenExchange", "inputs": [{"type": "address", "name": "buyer", "indexed": true}, {"type": "int128", "name": "sold_id", "indexed": false}, {"type": "uint256", "name": "tokens_sold", "indexed": false}, {"type": "int128", "name": "bought_id", "indexed": false}, {"type": "uint256", "name": "tokens_bought", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "AddLiquidity", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256[2]", "name": "token_amounts", "indexed": false}, {"type": "uint256[2]", "name": "fees", "indexed": false}, {"type": "uint256", "name": "invariant", "indexed": false}, {"type": "uint256", "name": "token_supply", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RemoveLiquidity", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256[2]", "name": "token_amounts", "indexed": false}, {"type": "uint256[2]", "name": "fees", "indexed": false}, {"type": "uint256", "name": "token_supply", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RemoveLiquidityOne", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256", "name": "token_amount", "indexed": false}, {"type": "uint256", "name": "coin_amount", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RemoveLiquidityImbalance", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256[2]", "name": "token_amounts", "indexed": false}, {"type": "uint256[2]", "name": "fees", "indexed": false}, {"type": "uint256", "name": "invariant", "indexed": false}, {"type": "uint256", "name": "token_supply", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "CommitNewAdmin", "inputs": [{"type": "uint256", "name": "deadline", "indexed": true}, {"type": "address", "name": "admin", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "NewAdmin", "inputs": [{"type": "address", "name": "admin", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "CommitNewFee", "inputs": [{"type": "uint256", "name": "deadline", "indexed": true}, {"type": "uint256", "name": "fee", "indexed": false}, {"type": "uint256", "name": "admin_fee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "NewFee", "inputs": [{"type": "uint256", "name": "fee", "indexed": false}, {"type": "uint256", "name": "admin_fee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RampA", "inputs": [{"type": "uint256", "name": "old_A", "indexed": false}, {"type": "uint256", "name": "new_A", "indexed": false}, {"type": "uint256", "name": "initial_time", "indexed": false}, {"type": "uint256", "name": "future_time", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StopRampA", "inputs": [{"type": "uint256", "name": "A", "indexed": false}, {"type": "uint256", "name": "t", "indexed": false}], "anonymous": false, "type": "event"}, {"outputs": [], "inputs": [{"type": "address", "name": "_owner"}, {"type": "address[2]", "name": "_coins"}, {"type": "address", "name": "_pool_token"}, {"type": "uint256", "name": "_A"}, {"type": "uint256", "name": "_fee"}, {"type": "uint256", "name": "_admin_fee"}], "stateMutability": "nonpayable", "type": "constructor"}, {"name": "A", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 5289}, {"name": "A_precise", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 5251}, {"name": "get_virtual_price", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 1007693}, {"name": "calc_token_amount", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "amounts"}, {"type": "bool", "name": "is_deposit"}], "stateMutability": "view", "type": "function", "gas": 2008092}, {"name": "add_liquidity", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "amounts"}, {"type": "uint256", "name": "min_mint_amount"}], "stateMutability": "payable", "type": "function", "gas": 3177140}, {"name": "get_dy", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}], "stateMutability": "view", "type": "function", "gas": 2444404}, {"name": "exchange", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}, {"type": "uint256", "name": "min_dy"}], "stateMutability": "payable", "type": "function", "gas": 2638539}, {"name": "remove_liquidity", "outputs": [{"type": "uint256[2]", "name": ""}], "inputs": [{"type": "uint256", "name": "_amount"}, {"type": "uint256[2]", "name": "min_amounts"}], "stateMutability": "nonpayable", "type": "function", "gas": 230063}, {"name": "remove_liquidity_imbalance", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "amounts"}, {"type": "uint256", "name": "max_burn_amount"}], "stateMutability": "nonpayable", "type": "function", "gas": 3240360}, {"name": "calc_withdraw_one_coin", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_token_amount"}, {"type": "int128", "name": "i"}], "stateMutability": "view", "type": "function", "gas": 1375}, {"name": "remove_liquidity_one_coin", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_token_amount"}, {"type": "int128", "name": "i"}, {"type": "uint256", "name": "_min_amount"}], "stateMutability": "nonpayable", "type": "function", "gas": 3899991}, {"name": "ramp_A", "outputs": [], "inputs": [{"type": "uint256", "name": "_future_A"}, {"type": "uint256", "name": "_future_time"}], "stateMutability": "nonpayable", "type": "function", "gas": 151774}, {"name": "stop_ramp_A", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 148535}, {"name": "commit_new_fee", "outputs": [], "inputs": [{"type": "uint256", "name": "new_fee"}, {"type": "uint256", "name": "new_admin_fee"}], "stateMutability": "nonpayable", "type": "function", "gas": 110371}, {"name": "apply_new_fee", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 153055}, {"name": "revert_new_parameters", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 21805}, {"name": "commit_transfer_ownership", "outputs": [], "inputs": [{"type": "address", "name": "_owner"}], "stateMutability": "nonpayable", "type": "function", "gas": 74543}, {"name": "apply_transfer_ownership", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 116523}, {"name": "revert_transfer_ownership", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 21895}, {"name": "admin_balances", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "i"}], "stateMutability": "view", "type": "function", "gas": 3453}, {"name": "withdraw_admin_fees", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 133752}, {"name": "donate_admin_fees", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 130899}, {"name": "kill_me", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 37908}, {"name": "unkill_me", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 22045}, {"name": "coins", "outputs": [{"type": "address", "name": ""}], "inputs": [{"type": "uint256", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 2130}, {"name": "balances", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 2160}, {"name": "fee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2081}, {"name": "admin_fee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2111}, {"name": "owner", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2141}, {"name": "initial_A", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2171}, {"name": "future_A", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2201}, {"name": "initial_A_time", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2231}, {"name": "future_A_time", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2261}, {"name": "admin_actions_deadline", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2291}, {"name": "transfer_ownership_deadline", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2321}, {"name": "future_fee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2351}, {"name": "future_admin_fee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2381}, {"name": "future_owner", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2411}], "YEARN_VAULT_V2": [{"name": "Transfer", "inputs": [{"type": "address", "name": "sender", "indexed": true}, {"type": "address", "name": "receiver", "indexed": true}, {"type": "uint256", "name": "value", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "Approval", "inputs": [{"type": "address", "name": "owner", "indexed": true}, {"type": "address", "name": "spender", "indexed": true}, {"type": "uint256", "name": "value", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StrategyAdded", "inputs": [{"type": "address", "name": "strategy", "indexed": true}, {"type": "uint256", "name": "debtRatio", "indexed": false}, {"type": "uint256", "name": "rateLimit", "indexed": false}, {"type": "uint256", "name": "performanceFee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StrategyReported", "inputs": [{"type": "address", "name": "strategy", "indexed": true}, {"type": "uint256", "name": "gain", "indexed": false}, {"type": "uint256", "name": "loss", "indexed": false}, {"type": "uint256", "name": "totalGain", "indexed": false}, {"type": "uint256", "name": "totalLoss", "indexed": false}, {"type": "uint256", "name": "totalDebt", "indexed": false}, {"type": "uint256", "name": "debtAdded", "indexed": false}, {"type": "uint256", "name": "debtRatio", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdateGovernance", "inputs": [{"type": "address", "name": "governance", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdateManagement", "inputs": [{"type": "address", "name": "management", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdateGuestList", "inputs": [{"type": "address", "name": "guestList", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdateRewards", "inputs": [{"type": "address", "name": "rewards", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdateDepositLimit", "inputs": [{"type": "uint256", "name": "depositLimit", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdatePerformanceFee", "inputs": [{"type": "uint256", "name": "performanceFee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdateManagementFee", "inputs": [{"type": "uint256", "name": "managementFee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "Update<PERSON><PERSON><PERSON>", "inputs": [{"type": "address", "name": "guardian", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "EmergencyShutdown", "inputs": [{"type": "bool", "name": "active", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "UpdateWithdrawalQueue", "inputs": [{"type": "address[20]", "name": "queue", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StrategyUpdateDebtRatio", "inputs": [{"type": "address", "name": "strategy", "indexed": true}, {"type": "uint256", "name": "debtRatio", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StrategyUpdateRateLimit", "inputs": [{"type": "address", "name": "strategy", "indexed": true}, {"type": "uint256", "name": "rateLimit", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StrategyUpdatePerformanceFee", "inputs": [{"type": "address", "name": "strategy", "indexed": true}, {"type": "uint256", "name": "performanceFee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StrategyMigrated", "inputs": [{"type": "address", "name": "oldVersion", "indexed": true}, {"type": "address", "name": "newVersion", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "StrategyRevoked", "inputs": [{"type": "address", "name": "strategy", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "StrategyRemovedFromQueue", "inputs": [{"type": "address", "name": "strategy", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "StrategyAddedToQueue", "inputs": [{"type": "address", "name": "strategy", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "initialize", "outputs": [], "inputs": [{"type": "address", "name": "token"}, {"type": "address", "name": "governance"}, {"type": "address", "name": "rewards"}, {"type": "string", "name": "nameOverride"}, {"type": "string", "name": "symbolOverride"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "initialize", "outputs": [], "inputs": [{"type": "address", "name": "token"}, {"type": "address", "name": "governance"}, {"type": "address", "name": "rewards"}, {"type": "string", "name": "nameOverride"}, {"type": "string", "name": "symbolOverride"}, {"type": "address", "name": "guardian"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "apiVersion", "outputs": [{"type": "string", "name": ""}], "inputs": [], "stateMutability": "pure", "type": "function", "gas": 4519}, {"name": "setName", "outputs": [], "inputs": [{"type": "string", "name": "name"}], "stateMutability": "nonpayable", "type": "function", "gas": 107017}, {"name": "setSymbol", "outputs": [], "inputs": [{"type": "string", "name": "symbol"}], "stateMutability": "nonpayable", "type": "function", "gas": 71867}, {"name": "setGovernance", "outputs": [], "inputs": [{"type": "address", "name": "governance"}], "stateMutability": "nonpayable", "type": "function", "gas": 36338}, {"name": "acceptGovernance", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 37610}, {"name": "setManagement", "outputs": [], "inputs": [{"type": "address", "name": "management"}], "stateMutability": "nonpayable", "type": "function", "gas": 37748}, {"name": "setGuestList", "outputs": [], "inputs": [{"type": "address", "name": "guestList"}], "stateMutability": "nonpayable", "type": "function", "gas": 37778}, {"name": "setRewards", "outputs": [], "inputs": [{"type": "address", "name": "rewards"}], "stateMutability": "nonpayable", "type": "function", "gas": 37808}, {"name": "setDepositLimit", "outputs": [], "inputs": [{"type": "uint256", "name": "limit"}], "stateMutability": "nonpayable", "type": "function", "gas": 37738}, {"name": "setPerformanceFee", "outputs": [], "inputs": [{"type": "uint256", "name": "fee"}], "stateMutability": "nonpayable", "type": "function", "gas": 37872}, {"name": "setManagementFee", "outputs": [], "inputs": [{"type": "uint256", "name": "fee"}], "stateMutability": "nonpayable", "type": "function", "gas": 37902}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "inputs": [{"type": "address", "name": "guardian"}], "stateMutability": "nonpayable", "type": "function", "gas": 39146}, {"name": "setEmergencyShutdown", "outputs": [], "inputs": [{"type": "bool", "name": "active"}], "stateMutability": "nonpayable", "type": "function", "gas": 39217}, {"name": "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "inputs": [{"type": "address[20]", "name": "queue"}], "stateMutability": "nonpayable", "type": "function", "gas": 763893}, {"name": "transfer", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "receiver"}, {"type": "uint256", "name": "amount"}], "stateMutability": "nonpayable", "type": "function", "gas": 76733}, {"name": "transferFrom", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "sender"}, {"type": "address", "name": "receiver"}, {"type": "uint256", "name": "amount"}], "stateMutability": "nonpayable", "type": "function", "gas": 116496}, {"name": "approve", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "spender"}, {"type": "uint256", "name": "amount"}], "stateMutability": "nonpayable", "type": "function", "gas": 38244}, {"name": "increaseAllowance", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "spender"}, {"type": "uint256", "name": "amount"}], "stateMutability": "nonpayable", "type": "function", "gas": 40285}, {"name": "decreaseAllowance", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "spender"}, {"type": "uint256", "name": "amount"}], "stateMutability": "nonpayable", "type": "function", "gas": 40309}, {"name": "permit", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "owner"}, {"type": "address", "name": "spender"}, {"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "expiry"}, {"type": "bytes", "name": "signature"}], "stateMutability": "nonpayable", "type": "function", "gas": 81237}, {"name": "totalAssets", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 4123}, {"name": "deposit", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "nonpayable", "type": "function"}, {"name": "deposit", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_amount"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "deposit", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_amount"}, {"type": "address", "name": "recipient"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "maxAvailableShares", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 364171}, {"name": "withdraw", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "nonpayable", "type": "function"}, {"name": "withdraw", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "maxShares"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "withdraw", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "maxShares"}, {"type": "address", "name": "recipient"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "withdraw", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "maxShares"}, {"type": "address", "name": "recipient"}, {"type": "uint256", "name": "max<PERSON><PERSON>"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "pricePerShare", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 12412}, {"name": "addStrategy", "outputs": [], "inputs": [{"type": "address", "name": "strategy"}, {"type": "uint256", "name": "debtRatio"}, {"type": "uint256", "name": "rateLimit"}, {"type": "uint256", "name": "performanceFee"}], "stateMutability": "nonpayable", "type": "function", "gas": 1450351}, {"name": "updateStrategyDebtRatio", "outputs": [], "inputs": [{"type": "address", "name": "strategy"}, {"type": "uint256", "name": "debtRatio"}], "stateMutability": "nonpayable", "type": "function", "gas": 115316}, {"name": "updateStrategyRateLimit", "outputs": [], "inputs": [{"type": "address", "name": "strategy"}, {"type": "uint256", "name": "rateLimit"}], "stateMutability": "nonpayable", "type": "function", "gas": 41467}, {"name": "updateStrategyPerformanceFee", "outputs": [], "inputs": [{"type": "address", "name": "strategy"}, {"type": "uint256", "name": "performanceFee"}], "stateMutability": "nonpayable", "type": "function", "gas": 41344}, {"name": "migrateStrategy", "outputs": [], "inputs": [{"type": "address", "name": "oldVersion"}, {"type": "address", "name": "newVersion"}], "stateMutability": "nonpayable", "type": "function", "gas": 1105801}, {"name": "revokeStrategy", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function"}, {"name": "revokeStrategy", "outputs": [], "inputs": [{"type": "address", "name": "strategy"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "addStrategyToQueue", "outputs": [], "inputs": [{"type": "address", "name": "strategy"}], "stateMutability": "nonpayable", "type": "function", "gas": 1196920}, {"name": "removeStrategyFromQueue", "outputs": [], "inputs": [{"type": "address", "name": "strategy"}], "stateMutability": "nonpayable", "type": "function", "gas": 23091666}, {"name": "debtOutstanding", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function"}, {"name": "debtOutstanding", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "strategy"}], "stateMutability": "view", "type": "function"}, {"name": "creditAvailable", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function"}, {"name": "creditAvailable", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "strategy"}], "stateMutability": "view", "type": "function"}, {"name": "availableDepositLimit", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 9808}, {"name": "expectedReturn", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function"}, {"name": "expectedReturn", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "strategy"}], "stateMutability": "view", "type": "function"}, {"name": "report", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "gain"}, {"type": "uint256", "name": "loss"}, {"type": "uint256", "name": "_debtPayment"}], "stateMutability": "nonpayable", "type": "function", "gas": 937520}, {"name": "sweep", "outputs": [], "inputs": [{"type": "address", "name": "token"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "sweep", "outputs": [], "inputs": [{"type": "address", "name": "token"}, {"type": "uint256", "name": "amount"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "name", "outputs": [{"type": "string", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 9053}, {"name": "symbol", "outputs": [{"type": "string", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 8106}, {"name": "decimals", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2711}, {"name": "balanceOf", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 2956}, {"name": "allowance", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "arg0"}, {"type": "address", "name": "arg1"}], "stateMutability": "view", "type": "function", "gas": 3201}, {"name": "totalSupply", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2801}, {"name": "token", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2831}, {"name": "governance", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2861}, {"name": "management", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2891}, {"name": "guardian", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2921}, {"name": "guestList", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2951}, {"name": "strategies", "outputs": [{"type": "uint256", "name": "performanceFee"}, {"type": "uint256", "name": "activation"}, {"type": "uint256", "name": "debtRatio"}, {"type": "uint256", "name": "rateLimit"}, {"type": "uint256", "name": "lastReport"}, {"type": "uint256", "name": "totalDebt"}, {"type": "uint256", "name": "totalGain"}, {"type": "uint256", "name": "totalLoss"}], "inputs": [{"type": "address", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 10322}, {"name": "withdrawalQueue", "outputs": [{"type": "address", "name": ""}], "inputs": [{"type": "uint256", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 3120}, {"name": "emergencyShutdown", "outputs": [{"type": "bool", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3041}, {"name": "depositLimit", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3071}, {"name": "debtRatio", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3101}, {"name": "totalDebt", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3131}, {"name": "lastReport", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3161}, {"name": "activation", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3191}, {"name": "rewards", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3221}, {"name": "managementFee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3251}, {"name": "performanceFee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3281}, {"name": "nonces", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 3526}, {"name": "DOMAIN_SEPARATOR", "outputs": [{"type": "bytes32", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 3341}], "ATOKEN_V2": [{"inputs": [{"internalType": "contract ILendingPool", "name": "pool", "type": "address"}, {"internalType": "address", "name": "underlyingAssetAddress", "type": "address"}, {"internalType": "address", "name": "reserveTreasuryAddress", "type": "address"}, {"internalType": "string", "name": "tokenName", "type": "string"}, {"internalType": "string", "name": "tokenSymbol", "type": "string"}, {"internalType": "address", "name": "incentivesController", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "index", "type": "uint256"}], "name": "BalanceTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "index", "type": "uint256"}], "name": "Burn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "index", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "ATOKEN_REVISION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EIP712_REVISION", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PERMIT_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "POOL", "outputs": [{"internalType": "contract ILendingPool", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RESERVE_TREASURY_ADDRESS", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UINT_MAX_VALUE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UNDERLYING_ASSET_ADDRESS", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "_nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "address", "name": "receiverOfUnderlying", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getScaledUserBalanceAndSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "underlyingAssetDecimals", "type": "uint8"}, {"internalType": "string", "name": "tokenName", "type": "string"}, {"internalType": "string", "name": "tokenSymbol", "type": "string"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "mint", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "mintToTreasury", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "scaledBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "scaledTotalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferOnLiquidation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferUnderlyingTo", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}], "UNIV1_LP": [{"name": "TokenPurchase", "inputs": [{"type": "address", "name": "buyer", "indexed": true}, {"type": "uint256", "name": "eth_sold", "indexed": true}, {"type": "uint256", "name": "tokens_bought", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "EthPurchase", "inputs": [{"type": "address", "name": "buyer", "indexed": true}, {"type": "uint256", "name": "tokens_sold", "indexed": true}, {"type": "uint256", "name": "eth_bought", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "AddLiquidity", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256", "name": "eth_amount", "indexed": true}, {"type": "uint256", "name": "token_amount", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "RemoveLiquidity", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256", "name": "eth_amount", "indexed": true}, {"type": "uint256", "name": "token_amount", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "Transfer", "inputs": [{"type": "address", "name": "_from", "indexed": true}, {"type": "address", "name": "_to", "indexed": true}, {"type": "uint256", "name": "_value", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "Approval", "inputs": [{"type": "address", "name": "_owner", "indexed": true}, {"type": "address", "name": "_spender", "indexed": true}, {"type": "uint256", "name": "_value", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "setup", "outputs": [], "inputs": [{"type": "address", "name": "token_addr"}], "constant": false, "payable": false, "type": "function", "gas": 175875}, {"name": "addLiquidity", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "min_liquidity"}, {"type": "uint256", "name": "max_tokens"}, {"type": "uint256", "name": "deadline"}], "constant": false, "payable": true, "type": "function", "gas": 82616}, {"name": "removeLiquidity", "outputs": [{"type": "uint256", "name": "out"}, {"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "amount"}, {"type": "uint256", "name": "min_eth"}, {"type": "uint256", "name": "min_tokens"}, {"type": "uint256", "name": "deadline"}], "constant": false, "payable": false, "type": "function", "gas": 116814}, {"name": "__default__", "outputs": [], "inputs": [], "constant": false, "payable": true, "type": "function"}, {"name": "ethToTokenSwapInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "min_tokens"}, {"type": "uint256", "name": "deadline"}], "constant": false, "payable": true, "type": "function", "gas": 12757}, {"name": "ethToTokenTransferInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "min_tokens"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}], "constant": false, "payable": true, "type": "function", "gas": 12965}, {"name": "ethToTokenSwapOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_bought"}, {"type": "uint256", "name": "deadline"}], "constant": false, "payable": true, "type": "function", "gas": 50463}, {"name": "ethToTokenTransferOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_bought"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}], "constant": false, "payable": true, "type": "function", "gas": 50671}, {"name": "tokenToEthSwapInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_sold"}, {"type": "uint256", "name": "min_eth"}, {"type": "uint256", "name": "deadline"}], "constant": false, "payable": false, "type": "function", "gas": 47503}, {"name": "tokenToEthTransferInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_sold"}, {"type": "uint256", "name": "min_eth"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}], "constant": false, "payable": false, "type": "function", "gas": 47712}, {"name": "tokenToEthSwapOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "eth_bought"}, {"type": "uint256", "name": "max_tokens"}, {"type": "uint256", "name": "deadline"}], "constant": false, "payable": false, "type": "function", "gas": 50175}, {"name": "tokenToEthTransferOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "eth_bought"}, {"type": "uint256", "name": "max_tokens"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}], "constant": false, "payable": false, "type": "function", "gas": 50384}, {"name": "tokenToTokenSwapInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_sold"}, {"type": "uint256", "name": "min_tokens_bought"}, {"type": "uint256", "name": "min_eth_bought"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "token_addr"}], "constant": false, "payable": false, "type": "function", "gas": 51007}, {"name": "tokenToTokenTransferInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_sold"}, {"type": "uint256", "name": "min_tokens_bought"}, {"type": "uint256", "name": "min_eth_bought"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}, {"type": "address", "name": "token_addr"}], "constant": false, "payable": false, "type": "function", "gas": 51098}, {"name": "tokenToTokenSwapOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_bought"}, {"type": "uint256", "name": "max_tokens_sold"}, {"type": "uint256", "name": "max_eth_sold"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "token_addr"}], "constant": false, "payable": false, "type": "function", "gas": 54928}, {"name": "tokenToTokenTransferOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_bought"}, {"type": "uint256", "name": "max_tokens_sold"}, {"type": "uint256", "name": "max_eth_sold"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}, {"type": "address", "name": "token_addr"}], "constant": false, "payable": false, "type": "function", "gas": 55019}, {"name": "tokenToExchangeSwapInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_sold"}, {"type": "uint256", "name": "min_tokens_bought"}, {"type": "uint256", "name": "min_eth_bought"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "exchange_addr"}], "constant": false, "payable": false, "type": "function", "gas": 49342}, {"name": "tokenToExchangeTransferInput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_sold"}, {"type": "uint256", "name": "min_tokens_bought"}, {"type": "uint256", "name": "min_eth_bought"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}, {"type": "address", "name": "exchange_addr"}], "constant": false, "payable": false, "type": "function", "gas": 49532}, {"name": "tokenToExchangeSwapOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_bought"}, {"type": "uint256", "name": "max_tokens_sold"}, {"type": "uint256", "name": "max_eth_sold"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "exchange_addr"}], "constant": false, "payable": false, "type": "function", "gas": 53233}, {"name": "tokenToExchangeTransferOutput", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_bought"}, {"type": "uint256", "name": "max_tokens_sold"}, {"type": "uint256", "name": "max_eth_sold"}, {"type": "uint256", "name": "deadline"}, {"type": "address", "name": "recipient"}, {"type": "address", "name": "exchange_addr"}], "constant": false, "payable": false, "type": "function", "gas": 53423}, {"name": "getEthToTokenInputPrice", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "eth_sold"}], "constant": true, "payable": false, "type": "function", "gas": 5542}, {"name": "getEthToTokenOutputPrice", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_bought"}], "constant": true, "payable": false, "type": "function", "gas": 6872}, {"name": "getTokenToEthInputPrice", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "tokens_sold"}], "constant": true, "payable": false, "type": "function", "gas": 5637}, {"name": "getTokenToEthOutputPrice", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "uint256", "name": "eth_bought"}], "constant": true, "payable": false, "type": "function", "gas": 6897}, {"name": "tokenAddress", "outputs": [{"type": "address", "name": "out"}], "inputs": [], "constant": true, "payable": false, "type": "function", "gas": 1413}, {"name": "factoryAddress", "outputs": [{"type": "address", "name": "out"}], "inputs": [], "constant": true, "payable": false, "type": "function", "gas": 1443}, {"name": "balanceOf", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "address", "name": "_owner"}], "constant": true, "payable": false, "type": "function", "gas": 1645}, {"name": "transfer", "outputs": [{"type": "bool", "name": "out"}], "inputs": [{"type": "address", "name": "_to"}, {"type": "uint256", "name": "_value"}], "constant": false, "payable": false, "type": "function", "gas": 75034}, {"name": "transferFrom", "outputs": [{"type": "bool", "name": "out"}], "inputs": [{"type": "address", "name": "_from"}, {"type": "address", "name": "_to"}, {"type": "uint256", "name": "_value"}], "constant": false, "payable": false, "type": "function", "gas": 110907}, {"name": "approve", "outputs": [{"type": "bool", "name": "out"}], "inputs": [{"type": "address", "name": "_spender"}, {"type": "uint256", "name": "_value"}], "constant": false, "payable": false, "type": "function", "gas": 38769}, {"name": "allowance", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [{"type": "address", "name": "_owner"}, {"type": "address", "name": "_spender"}], "constant": true, "payable": false, "type": "function", "gas": 1925}, {"name": "name", "outputs": [{"type": "bytes32", "name": "out"}], "inputs": [], "constant": true, "payable": false, "type": "function", "gas": 1623}, {"name": "symbol", "outputs": [{"type": "bytes32", "name": "out"}], "inputs": [], "constant": true, "payable": false, "type": "function", "gas": 1653}, {"name": "decimals", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [], "constant": true, "payable": false, "type": "function", "gas": 1683}, {"name": "totalSupply", "outputs": [{"type": "uint256", "name": "out"}], "inputs": [], "constant": true, "payable": false, "type": "function", "gas": 1713}], "UNISWAP_V3_POOL": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "int24", "name": "tickLower", "type": "int24"}, {"indexed": true, "internalType": "int24", "name": "tickUpper", "type": "int24"}, {"indexed": false, "internalType": "uint128", "name": "amount", "type": "uint128"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}], "name": "Burn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": true, "internalType": "int24", "name": "tickLower", "type": "int24"}, {"indexed": true, "internalType": "int24", "name": "tickUpper", "type": "int24"}, {"indexed": false, "internalType": "uint128", "name": "amount0", "type": "uint128"}, {"indexed": false, "internalType": "uint128", "name": "amount1", "type": "uint128"}], "name": "Collect", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint128", "name": "amount0", "type": "uint128"}, {"indexed": false, "internalType": "uint128", "name": "amount1", "type": "uint128"}], "name": "CollectProtocol", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "paid0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "paid1", "type": "uint256"}], "name": "Flash", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "observationCardinalityNextOld", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "observationCardinalityNextNew", "type": "uint16"}], "name": "IncreaseObservationCardinalityNext", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint160", "name": "sqrtPriceX96", "type": "uint160"}, {"indexed": false, "internalType": "int24", "name": "tick", "type": "int24"}], "name": "Initialize", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "int24", "name": "tickLower", "type": "int24"}, {"indexed": true, "internalType": "int24", "name": "tickUpper", "type": "int24"}, {"indexed": false, "internalType": "uint128", "name": "amount", "type": "uint128"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "feeProtocol0Old", "type": "uint8"}, {"indexed": false, "internalType": "uint8", "name": "feeProtocol1Old", "type": "uint8"}, {"indexed": false, "internalType": "uint8", "name": "feeProtocol0New", "type": "uint8"}, {"indexed": false, "internalType": "uint8", "name": "feeProtocol1New", "type": "uint8"}], "name": "SetFeeProtocol", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "int256", "name": "amount0", "type": "int256"}, {"indexed": false, "internalType": "int256", "name": "amount1", "type": "int256"}, {"indexed": false, "internalType": "uint160", "name": "sqrtPriceX96", "type": "uint160"}, {"indexed": false, "internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"indexed": false, "internalType": "int24", "name": "tick", "type": "int24"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "int24", "name": "tickLower", "type": "int24"}, {"internalType": "int24", "name": "tickUpper", "type": "int24"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}], "name": "burn", "outputs": [{"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "int24", "name": "tickLower", "type": "int24"}, {"internalType": "int24", "name": "tickUpper", "type": "int24"}, {"internalType": "uint128", "name": "amount0Requested", "type": "uint128"}, {"internalType": "uint128", "name": "amount1Requested", "type": "uint128"}], "name": "collect", "outputs": [{"internalType": "uint128", "name": "amount0", "type": "uint128"}, {"internalType": "uint128", "name": "amount1", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint128", "name": "amount0Requested", "type": "uint128"}, {"internalType": "uint128", "name": "amount1Requested", "type": "uint128"}], "name": "collectProtocol", "outputs": [{"internalType": "uint128", "name": "amount0", "type": "uint128"}, {"internalType": "uint128", "name": "amount1", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "factory", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "fee", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeGrowthGlobal0X128", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeGrowthGlobal1X128", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "flash", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "observationCardinalityNext", "type": "uint16"}], "name": "increaseObservationCardinalityNext", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint160", "name": "sqrtPriceX96", "type": "uint160"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "liquidity", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxLiquidityPerTick", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "int24", "name": "tickLower", "type": "int24"}, {"internalType": "int24", "name": "tickUpper", "type": "int24"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "observations", "outputs": [{"internalType": "uint32", "name": "blockTimestamp", "type": "uint32"}, {"internalType": "int56", "name": "tickCumulative", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityCumulativeX128", "type": "uint160"}, {"internalType": "bool", "name": "initialized", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32[]", "name": "seconds<PERSON><PERSON>", "type": "uint32[]"}], "name": "observe", "outputs": [{"internalType": "int56[]", "name": "tickCumulatives", "type": "int56[]"}, {"internalType": "uint160[]", "name": "secondsPerLiquidityCumulativeX128s", "type": "uint160[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "positions", "outputs": [{"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint256", "name": "feeGrowthInside0LastX128", "type": "uint256"}, {"internalType": "uint256", "name": "feeGrowthInside1LastX128", "type": "uint256"}, {"internalType": "uint128", "name": "tokensOwed0", "type": "uint128"}, {"internalType": "uint128", "name": "tokensOwed1", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "protocolFees", "outputs": [{"internalType": "uint128", "name": "token0", "type": "uint128"}, {"internalType": "uint128", "name": "token1", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "feeProtocol0", "type": "uint8"}, {"internalType": "uint8", "name": "feeProtocol1", "type": "uint8"}], "name": "setFeeProtocol", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "slot0", "outputs": [{"internalType": "uint160", "name": "sqrtPriceX96", "type": "uint160"}, {"internalType": "int24", "name": "tick", "type": "int24"}, {"internalType": "uint16", "name": "observationIndex", "type": "uint16"}, {"internalType": "uint16", "name": "observationCardinality", "type": "uint16"}, {"internalType": "uint16", "name": "observationCardinalityNext", "type": "uint16"}, {"internalType": "uint8", "name": "feeProtocol", "type": "uint8"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "int24", "name": "tickLower", "type": "int24"}, {"internalType": "int24", "name": "tickUpper", "type": "int24"}], "name": "snapshotCumulativesInside", "outputs": [{"internalType": "int56", "name": "tickCumulativeInside", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityInsideX128", "type": "uint160"}, {"internalType": "uint32", "name": "secondsInside", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "int256", "name": "amountSpecified", "type": "int256"}, {"internalType": "uint160", "name": "sqrtPriceLimitX96", "type": "uint160"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "swap", "outputs": [{"internalType": "int256", "name": "amount0", "type": "int256"}, {"internalType": "int256", "name": "amount1", "type": "int256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "int16", "name": "", "type": "int16"}], "name": "tickBitmap", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tickSpacing", "outputs": [{"internalType": "int24", "name": "", "type": "int24"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "int24", "name": "", "type": "int24"}], "name": "ticks", "outputs": [{"internalType": "uint128", "name": "liquidityGross", "type": "uint128"}, {"internalType": "int128", "name": "liquidityNet", "type": "int128"}, {"internalType": "uint256", "name": "feeGrowthOutside0X128", "type": "uint256"}, {"internalType": "uint256", "name": "feeGrowthOutside1X128", "type": "uint256"}, {"internalType": "int56", "name": "tickCumulativeOutside", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityOutsideX128", "type": "uint160"}, {"internalType": "uint32", "name": "secondsOutside", "type": "uint32"}, {"internalType": "bool", "name": "initialized", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token0", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token1", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "CONVEX_LP_TOKEN": [{"name": "Transfer", "inputs": [{"type": "address", "name": "sender", "indexed": true}, {"type": "address", "name": "receiver", "indexed": true}, {"type": "uint256", "name": "value", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "Approval", "inputs": [{"type": "address", "name": "owner", "indexed": true}, {"type": "address", "name": "spender", "indexed": true}, {"type": "uint256", "name": "value", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "TokenExchange", "inputs": [{"type": "address", "name": "buyer", "indexed": true}, {"type": "int128", "name": "sold_id", "indexed": false}, {"type": "uint256", "name": "tokens_sold", "indexed": false}, {"type": "int128", "name": "bought_id", "indexed": false}, {"type": "uint256", "name": "tokens_bought", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "TokenExchangeUnderlying", "inputs": [{"type": "address", "name": "buyer", "indexed": true}, {"type": "int128", "name": "sold_id", "indexed": false}, {"type": "uint256", "name": "tokens_sold", "indexed": false}, {"type": "int128", "name": "bought_id", "indexed": false}, {"type": "uint256", "name": "tokens_bought", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "AddLiquidity", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256[2]", "name": "token_amounts", "indexed": false}, {"type": "uint256[2]", "name": "fees", "indexed": false}, {"type": "uint256", "name": "invariant", "indexed": false}, {"type": "uint256", "name": "token_supply", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RemoveLiquidity", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256[2]", "name": "token_amounts", "indexed": false}, {"type": "uint256[2]", "name": "fees", "indexed": false}, {"type": "uint256", "name": "token_supply", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RemoveLiquidityOne", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256", "name": "token_amount", "indexed": false}, {"type": "uint256", "name": "coin_amount", "indexed": false}, {"type": "uint256", "name": "token_supply", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RemoveLiquidityImbalance", "inputs": [{"type": "address", "name": "provider", "indexed": true}, {"type": "uint256[2]", "name": "token_amounts", "indexed": false}, {"type": "uint256[2]", "name": "fees", "indexed": false}, {"type": "uint256", "name": "invariant", "indexed": false}, {"type": "uint256", "name": "token_supply", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "CommitNewAdmin", "inputs": [{"type": "uint256", "name": "deadline", "indexed": true}, {"type": "address", "name": "admin", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "NewAdmin", "inputs": [{"type": "address", "name": "admin", "indexed": true}], "anonymous": false, "type": "event"}, {"name": "CommitNewFee", "inputs": [{"type": "uint256", "name": "deadline", "indexed": true}, {"type": "uint256", "name": "fee", "indexed": false}, {"type": "uint256", "name": "admin_fee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "NewFee", "inputs": [{"type": "uint256", "name": "fee", "indexed": false}, {"type": "uint256", "name": "admin_fee", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "RampA", "inputs": [{"type": "uint256", "name": "old_A", "indexed": false}, {"type": "uint256", "name": "new_A", "indexed": false}, {"type": "uint256", "name": "initial_time", "indexed": false}, {"type": "uint256", "name": "future_time", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "StopRampA", "inputs": [{"type": "uint256", "name": "A", "indexed": false}, {"type": "uint256", "name": "t", "indexed": false}], "anonymous": false, "type": "event"}, {"outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"name": "initialize", "outputs": [], "inputs": [{"type": "string", "name": "_name"}, {"type": "string", "name": "_symbol"}, {"type": "address", "name": "_coin"}, {"type": "uint256", "name": "_decimals"}, {"type": "uint256", "name": "_A"}, {"type": "uint256", "name": "_fee"}, {"type": "address", "name": "_admin"}], "stateMutability": "nonpayable", "type": "function", "gas": 470049}, {"name": "decimals", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 291}, {"name": "transfer", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "_to"}, {"type": "uint256", "name": "_value"}], "stateMutability": "nonpayable", "type": "function", "gas": 75402}, {"name": "transferFrom", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "_from"}, {"type": "address", "name": "_to"}, {"type": "uint256", "name": "_value"}], "stateMutability": "nonpayable", "type": "function", "gas": 112037}, {"name": "approve", "outputs": [{"type": "bool", "name": ""}], "inputs": [{"type": "address", "name": "_spender"}, {"type": "uint256", "name": "_value"}], "stateMutability": "nonpayable", "type": "function", "gas": 37854}, {"name": "get_previous_balances", "outputs": [{"type": "uint256[2]", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2254}, {"name": "get_balances", "outputs": [{"type": "uint256[2]", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2284}, {"name": "get_twap_balances", "outputs": [{"type": "uint256[2]", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "_first_balances"}, {"type": "uint256[2]", "name": "_last_balances"}, {"type": "uint256", "name": "_time_elapsed"}], "stateMutability": "view", "type": "function", "gas": 1522}, {"name": "get_price_cumulative_last", "outputs": [{"type": "uint256[2]", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2344}, {"name": "admin_fee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 621}, {"name": "A", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 5859}, {"name": "A_precise", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 5821}, {"name": "get_virtual_price", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 1011891}, {"name": "calc_token_amount", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "_amounts"}, {"type": "bool", "name": "_is_deposit"}], "stateMutability": "view", "type": "function"}, {"name": "calc_token_amount", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "_amounts"}, {"type": "bool", "name": "_is_deposit"}, {"type": "bool", "name": "_previous"}], "stateMutability": "view", "type": "function"}, {"name": "add_liquidity", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "_amounts"}, {"type": "uint256", "name": "_min_mint_amount"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "add_liquidity", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "_amounts"}, {"type": "uint256", "name": "_min_mint_amount"}, {"type": "address", "name": "_receiver"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "get_dy", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}], "stateMutability": "view", "type": "function"}, {"name": "get_dy", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}, {"type": "uint256[2]", "name": "_balances"}], "stateMutability": "view", "type": "function"}, {"name": "get_dy_underlying", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}], "stateMutability": "view", "type": "function"}, {"name": "get_dy_underlying", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}, {"type": "uint256[2]", "name": "_balances"}], "stateMutability": "view", "type": "function"}, {"name": "exchange", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}, {"type": "uint256", "name": "min_dy"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "exchange", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}, {"type": "uint256", "name": "min_dy"}, {"type": "address", "name": "_receiver"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "exchange_underlying", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}, {"type": "uint256", "name": "min_dy"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "exchange_underlying", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "int128", "name": "i"}, {"type": "int128", "name": "j"}, {"type": "uint256", "name": "dx"}, {"type": "uint256", "name": "min_dy"}, {"type": "address", "name": "_receiver"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "remove_liquidity", "outputs": [{"type": "uint256[2]", "name": ""}], "inputs": [{"type": "uint256", "name": "_burn_amount"}, {"type": "uint256[2]", "name": "_min_amounts"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "remove_liquidity", "outputs": [{"type": "uint256[2]", "name": ""}], "inputs": [{"type": "uint256", "name": "_burn_amount"}, {"type": "uint256[2]", "name": "_min_amounts"}, {"type": "address", "name": "_receiver"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "remove_liquidity_imbalance", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "_amounts"}, {"type": "uint256", "name": "_max_burn_amount"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "remove_liquidity_imbalance", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256[2]", "name": "_amounts"}, {"type": "uint256", "name": "_max_burn_amount"}, {"type": "address", "name": "_receiver"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "calc_withdraw_one_coin", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_burn_amount"}, {"type": "int128", "name": "i"}], "stateMutability": "view", "type": "function"}, {"name": "calc_withdraw_one_coin", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_burn_amount"}, {"type": "int128", "name": "i"}, {"type": "bool", "name": "_previous"}], "stateMutability": "view", "type": "function"}, {"name": "remove_liquidity_one_coin", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_burn_amount"}, {"type": "int128", "name": "i"}, {"type": "uint256", "name": "_min_received"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "remove_liquidity_one_coin", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "_burn_amount"}, {"type": "int128", "name": "i"}, {"type": "uint256", "name": "_min_received"}, {"type": "address", "name": "_receiver"}], "stateMutability": "nonpayable", "type": "function"}, {"name": "ramp_A", "outputs": [], "inputs": [{"type": "uint256", "name": "_future_A"}, {"type": "uint256", "name": "_future_time"}], "stateMutability": "nonpayable", "type": "function", "gas": 152464}, {"name": "stop_ramp_A", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 149225}, {"name": "admin_balances", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "i"}], "stateMutability": "view", "type": "function", "gas": 3601}, {"name": "withdraw_admin_fees", "outputs": [], "inputs": [], "stateMutability": "nonpayable", "type": "function", "gas": 11347}, {"name": "admin", "outputs": [{"type": "address", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2141}, {"name": "coins", "outputs": [{"type": "address", "name": ""}], "inputs": [{"type": "uint256", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 2280}, {"name": "balances", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "uint256", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 2310}, {"name": "fee", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2231}, {"name": "block_timestamp_last", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2261}, {"name": "initial_A", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2291}, {"name": "future_A", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2321}, {"name": "initial_A_time", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2351}, {"name": "future_A_time", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2381}, {"name": "name", "outputs": [{"type": "string", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 8813}, {"name": "symbol", "outputs": [{"type": "string", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 7866}, {"name": "balanceOf", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "arg0"}], "stateMutability": "view", "type": "function", "gas": 2686}, {"name": "allowance", "outputs": [{"type": "uint256", "name": ""}], "inputs": [{"type": "address", "name": "arg0"}, {"type": "address", "name": "arg1"}], "stateMutability": "view", "type": "function", "gas": 2931}, {"name": "totalSupply", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function", "gas": 2531}], "CURVE_REGISTRY": [{"name": "PoolAdded", "inputs": [{"name": "pool", "type": "address", "indexed": true}, {"name": "rate_method_id", "type": "bytes", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "PoolRemoved", "inputs": [{"name": "pool", "type": "address", "indexed": true}], "anonymous": false, "type": "event"}, {"stateMutability": "nonpayable", "type": "constructor", "inputs": [{"name": "_address_provider", "type": "address"}, {"name": "_gauge_controller", "type": "address"}], "outputs": []}, {"stateMutability": "view", "type": "function", "name": "find_pool_for_coins", "inputs": [{"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "view", "type": "function", "name": "find_pool_for_coins", "inputs": [{"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}, {"name": "i", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "view", "type": "function", "name": "get_n_coins", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[2]"}], "gas": 1521}, {"stateMutability": "view", "type": "function", "name": "get_coins", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address[8]"}], "gas": 12102}, {"stateMutability": "view", "type": "function", "name": "get_underlying_coins", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address[8]"}], "gas": 12194}, {"stateMutability": "view", "type": "function", "name": "get_decimals", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 7874}, {"stateMutability": "view", "type": "function", "name": "get_underlying_decimals", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 7966}, {"stateMutability": "view", "type": "function", "name": "get_rates", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 36992}, {"stateMutability": "view", "type": "function", "name": "get_gauges", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address[10]"}, {"name": "", "type": "int128[10]"}], "gas": 20157}, {"stateMutability": "view", "type": "function", "name": "get_balances", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 16583}, {"stateMutability": "view", "type": "function", "name": "get_underlying_balances", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 162842}, {"stateMutability": "view", "type": "function", "name": "get_virtual_price_from_lp_token", "inputs": [{"name": "_token", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 1927}, {"stateMutability": "view", "type": "function", "name": "get_A", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 1045}, {"stateMutability": "view", "type": "function", "name": "get_parameters", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "A", "type": "uint256"}, {"name": "future_A", "type": "uint256"}, {"name": "fee", "type": "uint256"}, {"name": "admin_fee", "type": "uint256"}, {"name": "future_fee", "type": "uint256"}, {"name": "future_admin_fee", "type": "uint256"}, {"name": "future_owner", "type": "address"}, {"name": "initial_A", "type": "uint256"}, {"name": "initial_A_time", "type": "uint256"}, {"name": "future_A_time", "type": "uint256"}], "gas": 6305}, {"stateMutability": "view", "type": "function", "name": "get_fees", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[2]"}], "gas": 1450}, {"stateMutability": "view", "type": "function", "name": "get_admin_balances", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 36454}, {"stateMutability": "view", "type": "function", "name": "get_coin_indices", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}], "outputs": [{"name": "", "type": "int128"}, {"name": "", "type": "int128"}, {"name": "", "type": "bool"}], "gas": 27131}, {"stateMutability": "view", "type": "function", "name": "estimate_gas_used", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 32004}, {"stateMutability": "view", "type": "function", "name": "is_meta", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "bool"}], "gas": 1900}, {"stateMutability": "view", "type": "function", "name": "get_pool_name", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "string"}], "gas": 8323}, {"stateMutability": "view", "type": "function", "name": "get_coin_swap_count", "inputs": [{"name": "_coin", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 1951}, {"stateMutability": "view", "type": "function", "name": "get_coin_swap_complement", "inputs": [{"name": "_coin", "type": "address"}, {"name": "_index", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}], "gas": 2090}, {"stateMutability": "view", "type": "function", "name": "get_pool_asset_type", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 2011}, {"stateMutability": "nonpayable", "type": "function", "name": "add_pool", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_n_coins", "type": "uint256"}, {"name": "_lp_token", "type": "address"}, {"name": "_rate_info", "type": "bytes32"}, {"name": "_decimals", "type": "uint256"}, {"name": "_underlying_decimals", "type": "uint256"}, {"name": "_has_initial_A", "type": "bool"}, {"name": "_is_v1", "type": "bool"}, {"name": "_name", "type": "string"}], "outputs": [], "gas": 61485845}, {"stateMutability": "nonpayable", "type": "function", "name": "add_pool_without_underlying", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_n_coins", "type": "uint256"}, {"name": "_lp_token", "type": "address"}, {"name": "_rate_info", "type": "bytes32"}, {"name": "_decimals", "type": "uint256"}, {"name": "_use_rates", "type": "uint256"}, {"name": "_has_initial_A", "type": "bool"}, {"name": "_is_v1", "type": "bool"}, {"name": "_name", "type": "string"}], "outputs": [], "gas": 31306062}, {"stateMutability": "nonpayable", "type": "function", "name": "add_metapool", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_n_coins", "type": "uint256"}, {"name": "_lp_token", "type": "address"}, {"name": "_decimals", "type": "uint256"}, {"name": "_name", "type": "string"}], "outputs": []}, {"stateMutability": "nonpayable", "type": "function", "name": "add_metapool", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_n_coins", "type": "uint256"}, {"name": "_lp_token", "type": "address"}, {"name": "_decimals", "type": "uint256"}, {"name": "_name", "type": "string"}, {"name": "_base_pool", "type": "address"}], "outputs": []}, {"stateMutability": "nonpayable", "type": "function", "name": "remove_pool", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [], "gas": 779731418758}, {"stateMutability": "nonpayable", "type": "function", "name": "set_pool_gas_estimates", "inputs": [{"name": "_addr", "type": "address[5]"}, {"name": "_amount", "type": "uint256[2][5]"}], "outputs": [], "gas": 390460}, {"stateMutability": "nonpayable", "type": "function", "name": "set_coin_gas_estimates", "inputs": [{"name": "_addr", "type": "address[10]"}, {"name": "_amount", "type": "uint256[10]"}], "outputs": [], "gas": 392047}, {"stateMutability": "nonpayable", "type": "function", "name": "set_gas_estimate_contract", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_estimator", "type": "address"}], "outputs": [], "gas": 72629}, {"stateMutability": "nonpayable", "type": "function", "name": "set_liquidity_gauges", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_liquidity_gauges", "type": "address[10]"}], "outputs": [], "gas": 400675}, {"stateMutability": "nonpayable", "type": "function", "name": "set_pool_asset_type", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_asset_type", "type": "uint256"}], "outputs": [], "gas": 72667}, {"stateMutability": "nonpayable", "type": "function", "name": "batch_set_pool_asset_type", "inputs": [{"name": "_pools", "type": "address[32]"}, {"name": "_asset_types", "type": "uint256[32]"}], "outputs": [], "gas": 1173447}, {"stateMutability": "view", "type": "function", "name": "address_provider", "inputs": [], "outputs": [{"name": "", "type": "address"}], "gas": 2048}, {"stateMutability": "view", "type": "function", "name": "gauge_controller", "inputs": [], "outputs": [{"name": "", "type": "address"}], "gas": 2078}, {"stateMutability": "view", "type": "function", "name": "pool_list", "inputs": [{"name": "arg0", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}], "gas": 2217}, {"stateMutability": "view", "type": "function", "name": "pool_count", "inputs": [], "outputs": [{"name": "", "type": "uint256"}], "gas": 2138}, {"stateMutability": "view", "type": "function", "name": "coin_count", "inputs": [], "outputs": [{"name": "", "type": "uint256"}], "gas": 2168}, {"stateMutability": "view", "type": "function", "name": "get_coin", "inputs": [{"name": "arg0", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}], "gas": 2307}, {"stateMutability": "view", "type": "function", "name": "get_pool_from_lp_token", "inputs": [{"name": "arg0", "type": "address"}], "outputs": [{"name": "", "type": "address"}], "gas": 2443}, {"stateMutability": "view", "type": "function", "name": "get_lp_token", "inputs": [{"name": "arg0", "type": "address"}], "outputs": [{"name": "", "type": "address"}], "gas": 2473}, {"stateMutability": "view", "type": "function", "name": "last_updated", "inputs": [], "outputs": [{"name": "", "type": "uint256"}], "gas": 2288}], "CURVE_METAPOOL_FACTORY": [{"name": "BasePoolAdded", "inputs": [{"name": "base_pool", "type": "address", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "PlainPoolDeployed", "inputs": [{"name": "coins", "type": "address[4]", "indexed": false}, {"name": "A", "type": "uint256", "indexed": false}, {"name": "fee", "type": "uint256", "indexed": false}, {"name": "deployer", "type": "address", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "MetaPoolDeployed", "inputs": [{"name": "coin", "type": "address", "indexed": false}, {"name": "base_pool", "type": "address", "indexed": false}, {"name": "A", "type": "uint256", "indexed": false}, {"name": "fee", "type": "uint256", "indexed": false}, {"name": "deployer", "type": "address", "indexed": false}], "anonymous": false, "type": "event"}, {"name": "LiquidityGaugeDeployed", "inputs": [{"name": "pool", "type": "address", "indexed": false}, {"name": "gauge", "type": "address", "indexed": false}], "anonymous": false, "type": "event"}, {"stateMutability": "nonpayable", "type": "constructor", "inputs": [{"name": "_fee_receiver", "type": "address"}], "outputs": []}, {"stateMutability": "view", "type": "function", "name": "metapool_implementations", "inputs": [{"name": "_base_pool", "type": "address"}], "outputs": [{"name": "", "type": "address[10]"}], "gas": 21716}, {"stateMutability": "view", "type": "function", "name": "find_pool_for_coins", "inputs": [{"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "view", "type": "function", "name": "find_pool_for_coins", "inputs": [{"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}, {"name": "i", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "view", "type": "function", "name": "get_base_pool", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address"}], "gas": 2663}, {"stateMutability": "view", "type": "function", "name": "get_n_coins", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 2699}, {"stateMutability": "view", "type": "function", "name": "get_meta_n_coins", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}], "gas": 5201}, {"stateMutability": "view", "type": "function", "name": "get_coins", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address[4]"}], "gas": 9164}, {"stateMutability": "view", "type": "function", "name": "get_underlying_coins", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address[8]"}], "gas": 21345}, {"stateMutability": "view", "type": "function", "name": "get_decimals", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[4]"}], "gas": 20185}, {"stateMutability": "view", "type": "function", "name": "get_underlying_decimals", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 19730}, {"stateMutability": "view", "type": "function", "name": "get_metapool_rates", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[2]"}], "gas": 5281}, {"stateMutability": "view", "type": "function", "name": "get_balances", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[4]"}], "gas": 20435}, {"stateMutability": "view", "type": "function", "name": "get_underlying_balances", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[8]"}], "gas": 39733}, {"stateMutability": "view", "type": "function", "name": "get_A", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 3135}, {"stateMutability": "view", "type": "function", "name": "get_fees", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}], "gas": 5821}, {"stateMutability": "view", "type": "function", "name": "get_admin_balances", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256[4]"}], "gas": 13535}, {"stateMutability": "view", "type": "function", "name": "get_coin_indices", "inputs": [{"name": "_pool", "type": "address"}, {"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}], "outputs": [{"name": "", "type": "int128"}, {"name": "", "type": "int128"}, {"name": "", "type": "bool"}], "gas": 33407}, {"stateMutability": "view", "type": "function", "name": "get_gauge", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address"}], "gas": 3089}, {"stateMutability": "view", "type": "function", "name": "get_implementation_address", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address"}], "gas": 3119}, {"stateMutability": "view", "type": "function", "name": "is_meta", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "bool"}], "gas": 3152}, {"stateMutability": "view", "type": "function", "name": "get_pool_asset_type", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "uint256"}], "gas": 5450}, {"stateMutability": "view", "type": "function", "name": "get_fee_receiver", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address"}], "gas": 5480}, {"stateMutability": "nonpayable", "type": "function", "name": "deploy_plain_pool", "inputs": [{"name": "_name", "type": "string"}, {"name": "_symbol", "type": "string"}, {"name": "_coins", "type": "address[4]"}, {"name": "_A", "type": "uint256"}, {"name": "_fee", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "nonpayable", "type": "function", "name": "deploy_plain_pool", "inputs": [{"name": "_name", "type": "string"}, {"name": "_symbol", "type": "string"}, {"name": "_coins", "type": "address[4]"}, {"name": "_A", "type": "uint256"}, {"name": "_fee", "type": "uint256"}, {"name": "_asset_type", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "nonpayable", "type": "function", "name": "deploy_plain_pool", "inputs": [{"name": "_name", "type": "string"}, {"name": "_symbol", "type": "string"}, {"name": "_coins", "type": "address[4]"}, {"name": "_A", "type": "uint256"}, {"name": "_fee", "type": "uint256"}, {"name": "_asset_type", "type": "uint256"}, {"name": "_implementation_idx", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "nonpayable", "type": "function", "name": "deploy_metapool", "inputs": [{"name": "_base_pool", "type": "address"}, {"name": "_name", "type": "string"}, {"name": "_symbol", "type": "string"}, {"name": "_coin", "type": "address"}, {"name": "_A", "type": "uint256"}, {"name": "_fee", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "nonpayable", "type": "function", "name": "deploy_metapool", "inputs": [{"name": "_base_pool", "type": "address"}, {"name": "_name", "type": "string"}, {"name": "_symbol", "type": "string"}, {"name": "_coin", "type": "address"}, {"name": "_A", "type": "uint256"}, {"name": "_fee", "type": "uint256"}, {"name": "_implementation_idx", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}]}, {"stateMutability": "nonpayable", "type": "function", "name": "deploy_gauge", "inputs": [{"name": "_pool", "type": "address"}], "outputs": [{"name": "", "type": "address"}], "gas": 93079}, {"stateMutability": "nonpayable", "type": "function", "name": "add_base_pool", "inputs": [{"name": "_base_pool", "type": "address"}, {"name": "_fee_receiver", "type": "address"}, {"name": "_asset_type", "type": "uint256"}, {"name": "_implementations", "type": "address[10]"}], "outputs": [], "gas": 1206132}, {"stateMutability": "nonpayable", "type": "function", "name": "set_metapool_implementations", "inputs": [{"name": "_base_pool", "type": "address"}, {"name": "_implementations", "type": "address[10]"}], "outputs": [], "gas": 382072}, {"stateMutability": "nonpayable", "type": "function", "name": "set_plain_implementations", "inputs": [{"name": "_n_coins", "type": "uint256"}, {"name": "_implementations", "type": "address[10]"}], "outputs": [], "gas": 379687}, {"stateMutability": "nonpayable", "type": "function", "name": "set_gauge_implementation", "inputs": [{"name": "_gauge_implementation", "type": "address"}], "outputs": [], "gas": 38355}, {"stateMutability": "nonpayable", "type": "function", "name": "batch_set_pool_asset_type", "inputs": [{"name": "_pools", "type": "address[32]"}, {"name": "_asset_types", "type": "uint256[32]"}], "outputs": [], "gas": 1139545}, {"stateMutability": "nonpayable", "type": "function", "name": "commit_transfer_ownership", "inputs": [{"name": "_addr", "type": "address"}], "outputs": [], "gas": 38415}, {"stateMutability": "nonpayable", "type": "function", "name": "accept_transfer_ownership", "inputs": [], "outputs": [], "gas": 58366}, {"stateMutability": "nonpayable", "type": "function", "name": "set_manager", "inputs": [{"name": "_manager", "type": "address"}], "outputs": [], "gas": 40996}, {"stateMutability": "nonpayable", "type": "function", "name": "set_fee_receiver", "inputs": [{"name": "_base_pool", "type": "address"}, {"name": "_fee_receiver", "type": "address"}], "outputs": [], "gas": 38770}, {"stateMutability": "nonpayable", "type": "function", "name": "convert_metapool_fees", "inputs": [], "outputs": [{"name": "", "type": "bool"}], "gas": 12880}, {"stateMutability": "nonpayable", "type": "function", "name": "add_existing_metapools", "inputs": [{"name": "_pools", "type": "address[10]"}], "outputs": [{"name": "", "type": "bool"}], "gas": 8610792}, {"stateMutability": "view", "type": "function", "name": "admin", "inputs": [], "outputs": [{"name": "", "type": "address"}], "gas": 3438}, {"stateMutability": "view", "type": "function", "name": "future_admin", "inputs": [], "outputs": [{"name": "", "type": "address"}], "gas": 3468}, {"stateMutability": "view", "type": "function", "name": "manager", "inputs": [], "outputs": [{"name": "", "type": "address"}], "gas": 3498}, {"stateMutability": "view", "type": "function", "name": "pool_list", "inputs": [{"name": "arg0", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}], "gas": 3573}, {"stateMutability": "view", "type": "function", "name": "pool_count", "inputs": [], "outputs": [{"name": "", "type": "uint256"}], "gas": 3558}, {"stateMutability": "view", "type": "function", "name": "base_pool_list", "inputs": [{"name": "arg0", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}], "gas": 3633}, {"stateMutability": "view", "type": "function", "name": "base_pool_count", "inputs": [], "outputs": [{"name": "", "type": "uint256"}], "gas": 3618}, {"stateMutability": "view", "type": "function", "name": "base_pool_assets", "inputs": [{"name": "arg0", "type": "address"}], "outputs": [{"name": "", "type": "bool"}], "gas": 3863}, {"stateMutability": "view", "type": "function", "name": "plain_implementations", "inputs": [{"name": "arg0", "type": "uint256"}, {"name": "arg1", "type": "uint256"}], "outputs": [{"name": "", "type": "address"}], "gas": 3838}, {"stateMutability": "view", "type": "function", "name": "gauge_implementation", "inputs": [], "outputs": [{"name": "", "type": "address"}], "gas": 3708}], "ERC721_TOKEN": [{"inputs": [{"internalType": "address", "name": "_factory", "type": "address"}, {"internalType": "address", "name": "_WETH9", "type": "address"}, {"internalType": "address", "name": "_tokenDescriptor_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "approved", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}], "name": "Collect", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}], "name": "DecreaseLiquidity", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"indexed": false, "internalType": "uint256", "name": "amount0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount1", "type": "uint256"}], "name": "IncreaseLiquidity", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PERMIT_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "WETH9", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "baseURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint128", "name": "amount0Max", "type": "uint128"}, {"internalType": "uint128", "name": "amount1Max", "type": "uint128"}], "internalType": "struct INonfungiblePositionManager.CollectParams", "name": "params", "type": "tuple"}], "name": "collect", "outputs": [{"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "uint24", "name": "fee", "type": "uint24"}, {"internalType": "uint160", "name": "sqrtPriceX96", "type": "uint160"}], "name": "createAndInitializePoolIfNecessary", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint256", "name": "amount0Min", "type": "uint256"}, {"internalType": "uint256", "name": "amount1Min", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "internalType": "struct INonfungiblePositionManager.DecreaseLiquidityParams", "name": "params", "type": "tuple"}], "name": "decreaseLiquidity", "outputs": [{"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "factory", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount0Desired", "type": "uint256"}, {"internalType": "uint256", "name": "amount1Desired", "type": "uint256"}, {"internalType": "uint256", "name": "amount0Min", "type": "uint256"}, {"internalType": "uint256", "name": "amount1Min", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "internalType": "struct INonfungiblePositionManager.IncreaseLiquidityParams", "name": "params", "type": "tuple"}], "name": "increaseLiquidity", "outputs": [{"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "uint24", "name": "fee", "type": "uint24"}, {"internalType": "int24", "name": "tickLower", "type": "int24"}, {"internalType": "int24", "name": "tickUpper", "type": "int24"}, {"internalType": "uint256", "name": "amount0Desired", "type": "uint256"}, {"internalType": "uint256", "name": "amount1Desired", "type": "uint256"}, {"internalType": "uint256", "name": "amount0Min", "type": "uint256"}, {"internalType": "uint256", "name": "amount1Min", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "internalType": "struct INonfungiblePositionManager.MintParams", "name": "params", "type": "tuple"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "name": "multicall", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "positions", "outputs": [{"internalType": "uint96", "name": "nonce", "type": "uint96"}, {"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "uint24", "name": "fee", "type": "uint24"}, {"internalType": "int24", "name": "tickLower", "type": "int24"}, {"internalType": "int24", "name": "tickUpper", "type": "int24"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint256", "name": "feeGrowthInside0LastX128", "type": "uint256"}, {"internalType": "uint256", "name": "feeGrowthInside1LastX128", "type": "uint256"}, {"internalType": "uint128", "name": "tokensOwed0", "type": "uint128"}, {"internalType": "uint128", "name": "tokensOwed1", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "refundETH", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "selfPermit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "selfPermitAllowed", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "selfPermitAllowedIfNecessary", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "selfPermitIfNecessary", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amountMinimum", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}], "name": "sweepToken", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "tokenByIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "tokenOfOwnerByIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount0Owed", "type": "uint256"}, {"internalType": "uint256", "name": "amount1Owed", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "uniswapV3MintCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountMinimum", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}], "name": "unwrapWETH9", "outputs": [], "stateMutability": "payable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]}