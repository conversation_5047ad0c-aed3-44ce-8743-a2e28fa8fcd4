INSERT OR REPLACE INTO location_asset_mappings (location, exchange_symbol, local_id) VALUES
(NULL, 'AXS', 'eip155:1/erc20:******************************************'),
(NULL, 'USDC', 'eip155:1/erc20:******************************************'),
(NULL, 'DAI', 'eip155:1/erc20:******************************************'),
(NULL, 'USDT', 'eip155:1/erc20:******************************************'),
(NULL, 'MATIC', 'eip155:1/erc20:******************************************'),
(NULL, 'YFI', 'eip155:1/erc20:******************************************'),
(NULL, 'WBTC', 'eip155:1/erc20:******************************************'),
(NULL, 'LINK', 'eip155:1/erc20:******************************************'),
(NULL, 'BAT', 'eip155:1/erc20:******************************************'),
(NULL, 'SUSHI', 'eip155:1/erc20:******************************************'),
(NULL, 'AAVE', 'eip155:1/erc20:******************************************'),
(NULL, '1INCH', 'eip155:1/erc20:******************************************'),
(NULL, 'UNI', 'eip155:1/erc20:******************************************'),
(NULL, 'BAL', 'eip155:1/erc20:******************************************'),
(NULL, 'FTM', 'eip155:1/erc20:******************************************'),
(NULL, 'MANA', 'eip155:1/erc20:******************************************'),
(NULL, 'LRC', 'eip155:1/erc20:******************************************'),
(NULL, 'GRT', 'eip155:1/erc20:******************************************'),
(NULL, 'KNC', 'eip155:1/erc20:******************************************'),
(NULL, 'COMP', 'eip155:1/erc20:0xc00e94Cb662C3520282E6f5717214004A7f26888'),
(NULL, 'SPELL', 'eip155:1/erc20:0x090185f2135308BaD17527004364eBcC2D37e5F6'),
(NULL, 'CRV', 'eip155:1/erc20:0xD533a949740bb3306d119CC777fa900bA034cd52'),
(NULL, 'SNX', 'eip155:1/erc20:0xC011a73ee8576Fb46F5E1c5751cA3B9Fe0af2a6F'),
(NULL, 'SAND', 'eip155:1/erc20:0x3845badAde8e6dFF049820680d1F14bD3903a5d0'),
(NULL, 'GALA', 'eip155:1/erc20:0x15D4c048F83bd7e37d49eA4C83a07267Ec4203dA'),
(NULL, 'T', 'eip155:1/erc20:0xCdF7028ceAB81fA0C6971208e83fa7872994beE5'),
(NULL, 'ARB', 'eip155:42161/erc20:0x912CE59144191C1204E64559FE8253a0e49E6548'),
(NULL, 'LDO', 'eip155:1/erc20:******************************************'),
(NULL, 'CHZ', 'eip155:1/erc20:******************************************'),
(NULL, 'PYUSD', 'eip155:1/erc20:******************************************'),
(NULL, 'LUNA', 'LUNA-2'),
(NULL, 'SOL', 'SOL-2'),
(NULL, 'STRK', 'STRK'),
('E', 'BCHABC', 'BCH'),
('E', 'BCHSV', 'BSV'),
('E', 'BQX', 'eip155:1/erc20:******************************************'),
('E', 'GXS', 'GXC'),
('E', 'YOYO', 'eip155:1/erc20:******************************************'),
('E', 'BETH', 'ETH2'),
('E', 'STX', 'STX-2'),
('E', 'ONE', 'ONE-2'),
('E', 'FTT', 'eip155:1/erc20:******************************************'),
('E', 'ADX', 'eip155:1/erc20:******************************************'),
('E', 'ANT', 'eip155:1/erc20:******************************************'),
('E', 'HOT', 'eip155:1/erc20:******************************************'),
('E', 'KEY', 'eip155:1/erc20:******************************************'),
('E', 'PNT', 'eip155:1/erc20:******************************************'),
('E', 'FET', 'eip155:1/erc20:******************************************'),
('E', 'TRB', 'eip155:1/erc20:******************************************'),
('E', 'WIN', 'WIN-3'),
('E', 'RCN', 'eip155:1/erc20:******************************************'),
('E', 'SLP', 'eip155:1/erc20:******************************************'),
('E', 'GTC', 'eip155:1/erc20:******************************************'),
('E', 'MOD', 'eip155:1/erc20:******************************************'),
('E', 'DATA', 'eip155:1/erc20:0x8f693ca8D21b157107184d29D398A8D082b38b76'),
('E', 'TCT', 'eip155:1/erc20:0x4824A7b64E3966B0133f4f4FFB1b9D6bEb75FFF7'),
('E', 'RARE', 'eip155:1/erc20:0xba5BDe662c17e2aDFF1075610382B9B691296350'),
('E', 'IMX', 'eip155:1/erc20:******************************************'),
('E', 'ALEPH', 'eip155:1/erc20:0x27702a26126e0B3702af63Ee09aC4d1A084EF628'),
('E', 'APENFT', 'NFT'),
('E', 'DAR', 'eip155:56/erc20:0x23CE9e926048273eF83be0A3A8Ba9Cb6D45cd978'),
('E', 'WRX', 'eip155:56/erc20:0x8e17ed70334C87eCE574C9d537BC153d8609e2a3'),
('E', 'MATH', 'eip155:1/erc20:0x08d967bb0134F2d07f7cfb6E246680c53927DD30'),
('E', 'PERP', 'eip155:1/erc20:******************************************'),
('E', 'SXP', 'eip155:1/erc20:******************************************'),
('E', 'FXS', 'eip155:1/erc20:******************************************'),
('E', 'CREAM', 'eip155:1/erc20:0x2ba592F78dB6436527729929AAf6c908497cB200'),
('E', 'ALPHA', 'eip155:1/erc20:******************************************'),
('E', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('E', 'BADGER', 'eip155:1/erc20:******************************************'),
('E', 'C98', 'eip155:1/erc20:******************************************'),
('E', 'AMPL', 'eip155:1/erc20:0xD46bA6D942050d489DBd938a2C909A5d5039A161'),
('E', 'BUSD', 'eip155:1/erc20:******************************************'),
('E', 'GHST', 'eip155:1/erc20:******************************************'),
('E', 'INJ', 'eip155:1/erc20:******************************************'),
('E', 'LUSD', 'eip155:1/erc20:******************************************'),
('E', 'QUICK', 'eip155:1/erc20:******************************************'),
('E', 'REQ', 'eip155:1/erc20:******************************************'),
('E', 'TUSD', 'eip155:1/erc20:******************************************'),
('E', 'WETH', 'eip155:1/erc20:******************************************'),
('E', 'MASK', 'eip155:1/erc20:******************************************'),
('E', 'TORN', 'eip155:1/erc20:******************************************'),
('E', 'METIS', 'eip155:1/erc20:******************************************'),
('E', 'GNO', 'eip155:1/erc20:******************************************'),
('E', 'HIGH', 'eip155:1/erc20:******************************************'),
('E', 'FRAX', 'eip155:1/erc20:******************************************'),
('E', 'ANKR', 'eip155:1/erc20:******************************************'),
('E', 'SUSD', 'eip155:1/erc20:******************************************'),
('E', 'RAMP', 'eip155:1/erc20:******************************************'),
('E', 'MIR', 'eip155:1/erc20:******************************************'),
('E', 'BIFI', 'eip155:56/erc20:******************************************'),
('E', 'CTSI', 'eip155:1/erc20:******************************************'),
('E', 'ACH', 'eip155:1/erc20:******************************************'),
('E', 'DIA', 'eip155:1/erc20:******************************************'),
('E', 'FARM', 'eip155:1/erc20:******************************************'),
('E', 'MC', 'eip155:1/erc20:******************************************'),
('E', 'STG', 'eip155:1/erc20:******************************************'),
('E', 'ZIL', 'eip155:1/erc20:******************************************'),
('E', 'WOO', 'eip155:1/erc20:******************************************'),
('E', 'SUPER', 'eip155:1/erc20:******************************************'),
('E', 'RNDR', 'eip155:1/erc20:******************************************'),
('E', 'REEF', 'eip155:1/erc20:******************************************'),
('E', 'PYR', 'eip155:1/erc20:******************************************'),
('E', 'CELR', 'eip155:1/erc20:******************************************'),
('E', 'VITE', 'eip155:1/erc20:******************************************'),
('E', 'PERL', 'eip155:1/erc20:******************************************'),
('E', 'POLS', 'eip155:1/erc20:******************************************'),
('E', 'RENBTC', 'eip155:1/erc20:******************************************'),
('E', 'DODO', 'eip155:1/erc20:******************************************'),
('E', 'FOR', 'eip155:1/erc20:******************************************'),
('E', 'FRONT', 'eip155:1/erc20:******************************************'),
('E', 'GVT', 'eip155:1/erc20:******************************************'),
('E', 'IOTX', 'eip155:1/erc20:******************************************'),
('E', 'LINA', 'eip155:1/erc20:******************************************'),
('E', 'LTO', 'eip155:1/erc20:******************************************'),
('E', 'MULTI', 'eip155:1/erc20:******************************************'),
('E', 'NCASH', 'eip155:1/erc20:******************************************'),
('E', 'OM', 'eip155:1/erc20:******************************************'),
('E', 'PROM', 'eip155:1/erc20:******************************************'),
('E', 'PROS', 'eip155:1/erc20:******************************************'),
('E', 'RGT', 'eip155:1/erc20:******************************************'),
('E', 'TLM', 'eip155:1/erc20:0x888888848B652B3E3a0f34c96E00EEC0F3a23F72'),
('E', 'BEL', 'eip155:1/erc20:0xA91ac63D040dEB1b7A5E4d4134aD23eb0ba07e14'),
('E', 'ARPA', 'eip155:1/erc20:******************************************'),
('E', 'ATA', 'eip155:1/erc20:0xA2120b9e674d3fC3875f415A7DF52e382F141225'),
('E', 'BETA', 'eip155:1/erc20:0xBe1a001FE942f96Eea22bA08783140B9Dcc09D28'),
('E', 'BLZ', 'eip155:1/erc20:******************************************'),
('E', 'CHR', 'eip155:1/erc20:0x915044526758533dfB918ecEb6e44bc21632060D'),
('E', 'CLV', 'eip155:1/erc20:******************************************'),
('E', 'DEXE', 'eip155:1/erc20:0xde4EE8057785A7e8e800Db58F9784845A5C2Cbd6'),
('E', 'DF', 'eip155:1/erc20:0x431ad2ff6a9C365805eBaD47Ee021148d6f7DBe0'),
('E', 'DNT', 'eip155:1/erc20:******************************************'),
('E', 'EZ', 'eip155:1/erc20:0x00AbA6fE5557De1a1d565658cBDdddf7C710a1eb'),
('E', 'VIDT', 'eip155:1/erc20:0x445f51299Ef3307dBD75036dd896565F5B4BF7A5'),
('E', 'CXO', 'eip155:1/erc20:******************************************'),
('E', 'UFT', 'eip155:1/erc20:0x0202Be363B8a4820f3F4DE7FaF5224fF05943AB1'),
('E', 'REP', 'eip155:1/erc20:******************************************'),
('E', 'BOND', 'eip155:1/erc20:0x0391D2021f89DC339F60Fff84546EA23E337750f'),
('E', 'MLN', 'eip155:1/erc20:******************************************'),
('E', 'FUN', 'eip155:1/erc20:0x419D0d8BdD9aF5e606Ae2232ed285Aff190E711b'),
('E', 'ORN', 'eip155:1/erc20:******************************************'),
('E', 'HFT', 'eip155:1/erc20:******************************************'),
('E', 'MAGIC', 'eip155:1/erc20:******************************************'),
('E', 'AXL', 'eip155:1/erc20:******************************************'),
('E', 'ALPACA', 'eip155:56/erc20:******************************************'),
('E', 'GNS', 'eip155:137/erc20:******************************************'),
('E', 'SYN', 'eip155:1/erc20:******************************************'),
('E', 'USDS', 'eip155:1/erc20:******************************************'),
('E', 'FLOKI', 'eip155:56/erc20:******************************************'),
('E', 'RSR', 'eip155:1/erc20:******************************************'),
('E', 'WBETH', 'eip155:1/erc20:******************************************'),
('E', 'COMBO', 'eip155:1/erc20:******************************************'),
('E', 'WLD', 'eip155:1/erc20:******************************************'),
('E', 'PENDLE', 'eip155:1/erc20:******************************************'),
('E', 'SHIB', 'eip155:1/erc20:******************************************'),
('E', 'OP', 'eip155:10/erc20:******************************************'),
('E', 'PEPE', 'eip155:1/erc20:******************************************'),
('E', 'QI', 'eip155:43114/erc20:******************************************'),
('E', 'ERN', 'eip155:1/erc20:******************************************'),
('E', 'CYBER', 'eip155:56/erc20:******************************************'),
('E', 'AEUR', 'eip155:56/erc20:******************************************'),
('E', 'AST', 'eip155:1/erc20:******************************************'),
('E', 'ORBS', 'eip155:1/erc20:******************************************'),
('E', 'RONIN', 'RON'),
('E', 'FIS', 'eip155:1/erc20:******************************************'),
('E', 'BEAMX', 'eip155:1/erc20:0x62D0A8458eD7719FDAF978fe5929C6D342B0bFcE'),
('E', 'VANRY', 'eip155:1/erc20:0x8DE5B80a0C1B02Fe4976851D030B36122dbb8624'),
('E', 'PIXEL', 'eip155:1/erc20:0x3429d03c6F7521AeC737a0BBF2E5ddcef2C3Ae31'),
('T', 'UDC', 'eip155:1/erc20:******************************************'),
('T', 'UST', 'eip155:1/erc20:******************************************'),
('T', 'WBT', 'eip155:1/erc20:******************************************'),
('T', 'MNA', 'eip155:1/erc20:******************************************'),
('T', 'BCHN', 'BCH'),
('T', 'CNH', 'CNY'),
('T', 'DOG', 'DOGE'),
('T', 'GNY', 'eip155:1/erc20:0xb1f871Ae9462F1b2C6826E88A7827e76f86751d4'),
('T', 'REP', 'eip155:1/erc20:******************************************'),
('T', 'TRI', 'eip155:1/erc20:0x8B40761142B9aa6dc8964e61D0585995425C3D94'),
('T', 'ZBT', 'eip155:1/erc20:0xBd0793332e9fB844A52a205A233EF27a5b34B927'),
('T', 'GOT', 'eip155:1/erc20:0x613Fa2A6e6DAA70c659060E86bA1443D2679c9D7'),
('T', 'ANT', 'eip155:1/erc20:******************************************'),
('T', 'EDO', 'eip155:1/erc20:******************************************'),
('T', 'ORS', 'eip155:1/erc20:0xac2e58A06E6265F1Cf5084EE58da68e5d75b49CA'),
('T', 'FTT', 'eip155:1/erc20:******************************************'),
('T', 'FET', 'eip155:1/erc20:******************************************'),
('T', 'TERRAUST', 'UST'),
('T', 'DAT', 'eip155:1/erc20:0x8f693ca8D21b157107184d29D398A8D082b38b76'),
('T', 'BCHABC', 'XEC'),
('T', 'SPK', 'eip155:1/erc20:0x42d6622deCe394b54999Fbd73D108123806f6a18'),
('T', 'EUT', 'eip155:1/erc20:******************************************'),
('T', 'HIX', 'eip155:1/erc20:0xC4f6E93AEDdc11dc22268488465bAbcAF09399aC'),
('T', 'APENFT', 'NFT'),
('T', 'LUNA2', 'LUNA-3'),
('T', 'VRA', 'eip155:1/erc20:0xF411903cbC70a74d22900a5DE66A2dda66507255'),
('T', 'GNO', 'eip155:1/erc20:******************************************'),
('T', 'AMP', 'eip155:1/erc20:0xD46bA6D942050d489DBd938a2C909A5d5039A161'),
('T', 'TSD', 'eip155:1/erc20:******************************************'),
('T', 'ZIL', 'eip155:1/erc20:******************************************'),
('T', 'REQ', 'eip155:1/erc20:******************************************'),
('T', 'CEL', 'eip155:1/erc20:******************************************'),
('T', 'MIR', 'eip155:1/erc20:******************************************'),
('T', 'VEE', 'eip155:1/erc20:0x340D2bdE5Eb28c1eed91B2f790723E3B160613B7'),
('T', 'EUS', 'eip155:1/erc20:0xdB25f211AB05b1c97D595516F45794528a807ad8'),
('T', 'GXT', 'eip155:1/erc20:0x4674672BcDdDA2ea5300F5207E1158185c944bc0'),
('T', 'MIM', 'eip155:1/erc20:0x99D8a9C45b2ecA8864373A26D1459e3Dff1e17F3'),
('T', 'WOO', 'eip155:1/erc20:******************************************'),
('T', 'B2M', 'eip155:1/erc20:******************************************'),
('T', 'STG', 'eip155:1/erc20:******************************************'),
('T', 'ZCN', 'eip155:1/erc20:0xb9EF770B6A5e12E45983C5D80545258aA38F3B78'),
('T', 'KAI', 'eip155:1/erc20:0xD9Ec3ff1f8be459Bb9369b4E79e9Ebcf7141C093'),
('T', 'SXX', 'eip155:1/erc20:0x99fE3B1391503A1bC1788051347A1324bff41452'),
('T', 'REEF', 'eip155:1/erc20:******************************************'),
('T', 'POLC', 'eip155:1/erc20:0xaA8330FB2B4D5D07ABFE7A72262752a8505C6B37'),
('T', 'HMT', 'eip155:1/erc20:0xd1ba9BAC957322D6e8c07a160a3A8dA11A0d2867'),
('T', 'TRADE', 'eip155:1/erc20:0x6F87D756DAf0503d08Eb8993686c7Fc01Dc44fB1'),
('T', 'WILD', 'eip155:1/erc20:0x2a3bFF78B79A009976EeA096a51A948a3dC00e34'),
('T', 'RLC', 'eip155:1/erc20:******************************************'),
('T', 'XCAD', 'eip155:1/erc20:0x7659CE147D0e714454073a5dd7003544234b6Aa0'),
('T', 'FLOKI', 'eip155:56/erc20:******************************************'),
('T', 'ONE', 'ONE-2'),
('T', 'XAUT', 'eip155:1/erc20:0x4922a015c4407F87432B179bb209e125432E4a2A'),
('T', 'TON', 'eip155:1/erc20:******************************************'),
('T', 'LAI', 'eip155:1/erc20:0xD04E772BC0d591fBD288f2E2a86aFA3D3CB647F8'),
('T', 'GOC', 'eip155:56/erc20:0x4B85a666deC7C959e88b97814E46113601B07e57'),
('T', 'PEPE', 'eip155:1/erc20:******************************************'),
('T', 'INJ', 'eip155:1/erc20:******************************************'),
('T', 'ZETA', 'eip155:1/erc20:******************************************'),
('T', 'APP', 'eip155:1/erc20:******************************************'),
('b', 'MIOTA', 'IOTA'),
('b', 'PAN', 'eip155:1/erc20:0x536381a8628dBcC8C70aC9A30A7258442eAb4c92'),
('b', 'ANT', 'eip155:1/erc20:******************************************'),
('b', 'ONE', 'ONE-2'),
('b', 'REP', 'eip155:1/erc20:******************************************'),
('b', 'ZIL', 'eip155:1/erc20:******************************************'),
('b', 'ANKR', 'eip155:1/erc20:******************************************'),
('b', 'REQ', 'eip155:1/erc20:******************************************'),
('b', 'IMX', 'eip155:1/erc20:******************************************'),
('b', 'PERP', 'eip155:1/erc20:******************************************'),
('b', 'CTSI', 'eip155:1/erc20:******************************************'),
('b', 'RNDR', 'eip155:1/erc20:******************************************'),
('b', 'SXP', 'eip155:1/erc20:******************************************'),
('b', 'FXS', 'eip155:1/erc20:******************************************'),
('b', 'ACH', 'eip155:1/erc20:******************************************'),
('b', 'HIGH', 'eip155:1/erc20:******************************************'),
('b', 'SUPER', 'eip155:1/erc20:******************************************'),
('b', 'CELR', 'eip155:1/erc20:******************************************'),
('b', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('b', 'ARPA', 'eip155:1/erc20:******************************************'),
('b', 'BLZ', 'eip155:1/erc20:******************************************'),
('b', 'GNO', 'eip155:1/erc20:******************************************'),
('b', 'IOTX', 'eip155:1/erc20:******************************************'),
('b', 'ORBS', 'eip155:1/erc20:******************************************'),
('b', 'DODO', 'eip155:1/erc20:******************************************'),
('b', 'WOO', 'eip155:1/erc20:******************************************'),
('b', 'QUICK', 'eip155:1/erc20:******************************************'),
('b', 'LINA', 'eip155:1/erc20:******************************************'),
('b', 'C98', 'eip155:1/erc20:******************************************'),
('b', 'FARM', 'eip155:1/erc20:******************************************'),
('b', 'FET', 'eip155:1/erc20:******************************************'),
('b', 'GTC', 'eip155:1/erc20:******************************************'),
('b', 'FTT', 'eip155:1/erc20:******************************************'),
('b', 'CAKE', 'eip155:56/erc20:0x0E09FaBB73Bd3Ade0a17ECC321fD13a19e81cE82'),
('b', 'CYBER', 'eip155:56/erc20:******************************************'),
('b', 'PEPE', 'eip155:1/erc20:******************************************'),
('b', 'CLV', 'eip155:1/erc20:******************************************'),
('b', 'MASK', 'eip155:1/erc20:******************************************'),
('b', 'CEL', 'eip155:1/erc20:******************************************'),
('b', 'WLD', 'eip155:1/erc20:******************************************'),
('b', 'TUSD', 'eip155:1/erc20:******************************************'),
('R', 'FTT', 'eip155:1/erc20:******************************************'),
('R', 'FET', 'eip155:1/erc20:******************************************'),
('R', 'SLP', 'eip155:1/erc20:******************************************'),
('R', 'EURT', 'eip155:1/erc20:******************************************'),
('R', 'IMX', 'eip155:1/erc20:******************************************'),
('R', 'ANT', 'eip155:1/erc20:******************************************'),
('R', 'SXP', 'eip155:1/erc20:******************************************'),
('R', 'ALPHA', 'eip155:1/erc20:******************************************'),
('R', 'PERP', 'eip155:1/erc20:******************************************'),
('R', 'INJ', 'eip155:1/erc20:******************************************'),
('R', 'CEL', 'eip155:1/erc20:******************************************'),
('R', 'CTSI', 'eip155:1/erc20:******************************************'),
('R', 'RNDR', 'eip155:1/erc20:******************************************'),
('R', 'LMWR', 'eip155:1/erc20:******************************************'),
('R', 'PEPE', 'eip155:1/erc20:******************************************'),
('R', 'VEXT', 'eip155:1/erc20:******************************************'),
('D', 'BITS', 'BITS-2'),
('D', 'NBT', 'USNBT'),
('D', 'BTM', 'BTM-2'),
('D', 'PI', 'eip155:1/erc20:0xB9bb08AB7E9Fa0A1356bd4A39eC0ca267E03b0b3'),
('D', 'PLA', 'eip155:1/erc20:0x0198f46f520F33cd4329bd4bE380a25a90536CD5'),
('D', 'WAXP', 'eip155:1/erc20:0x39Bb259F66E1C59d5ABEF88375979b4D20D98022'),
('D', 'VAL', 'RADS'),
('D', 'ADX', 'eip155:1/erc20:******************************************'),
('D', 'AID', 'eip155:1/erc20:0x37E8789bB9996CaC9156cD5F5Fd32599E6b91289'),
('D', 'ANT', 'eip155:1/erc20:******************************************'),
('D', 'CMCT', 'eip155:1/erc20:0x47bc01597798DCD7506DCCA36ac4302fc93a8cFb'),
('D', 'REV', 'eip155:1/erc20:0x2ef52Ed7De8c5ce03a4eF0efbe9B7450F2D7Edc9'),
('D', 'VRA', 'eip155:1/erc20:0xF411903cbC70a74d22900a5DE66A2dda66507255'),
('D', 'FET', 'eip155:1/erc20:******************************************'),
('D', 'GNY', 'eip155:1/erc20:0xb1f871Ae9462F1b2C6826E88A7827e76f86751d4'),
('D', 'MTC', 'MTC-3'),
('D', 'EDGELESS', 'eip155:1/erc20:0x08711D3B02C8758F2FB3ab4e80228418a7F8e39c'),
('D', 'RCN', 'eip155:1/erc20:******************************************'),
('D', 'BLOC', 'eip155:1/erc20:0x6F919D67967a97EA36195A2346d9244E60FE0dDB'),
('D', 'EDR', 'eip155:1/erc20:0xc528c28FEC0A90C083328BC45f587eE215760A0F'),
('D', 'CPC', 'eip155:1/erc20:0xfAE4Ee59CDd86e3Be9e8b90b53AA866327D7c090'),
('D', 'COIN', 'COIN-2'),
('D', 'STCCOIN', 'eip155:1/erc20:0x15B543e986b8c34074DFc9901136d9355a537e7E'),
('D', 'STC', 'eip155:1/erc20:0x8f136Cc8bEf1fEA4A7b71aa2301ff1A52F084384'),
('D', 'MER', 'MER'),
('D', 'XGOLD', 'eip155:1/erc20:0x670f9D9a26D3D42030794ff035d35a67AA092ead'),
('D', 'CYCLUB', 'eip155:1/erc20:0x3b58c52C03ca5Eb619EBa171091c86C34d603e5f'),
('D', 'GOLD', 'eip155:1/erc20:0xE081b71Ed098FBe1108EA48e235b74F122272E68'),
('D', 'CURIO', 'eip155:1/erc20:0x13339fD07934CD674269726EdF3B5ccEE9DD93de'),
('D', 'MYCE', 'YCE'),
('D', 'CGT', 'eip155:1/erc20:0xF56b164efd3CFc02BA739b719B6526A6FA1cA32a'),
('D', 'MF1', 'eip155:1/erc20:0x9b5161a41B58498Eb9c5FEBf89d60714089d2253'),
('D', 'DTX', 'eip155:1/erc20:0x765f0C16D1Ddc279295c1a7C24B0883F62d33F75'),
('D', 'VCK', 'eip155:1/erc20:0xfa5B75a9e13Df9775cf5b996A049D9cc07c15731'),
('D', '_MOF', 'eip155:1/erc20:0x653430560bE843C4a3D143d0110e896c2Ab8ac0D'),
('D', 'ZKP', 'eip155:1/erc20:0x909E34d3f6124C324ac83DccA84b74398a6fa173'),
('D', 'CTC', 'eip155:1/erc20:******************************************'),
('D', 'GTC', 'eip155:1/erc20:******************************************'),
('D', 'ACH', 'eip155:1/erc20:******************************************'),
('D', 'ALTA', 'eip155:1/erc20:0xe0cCa86B254005889aC3a81e737f56a14f4A38F5'),
('D', 'ANKR', 'eip155:1/erc20:******************************************'),
('D', 'ARIA20', 'eip155:1/erc20:0xeDF6568618A00C6F0908Bf7758A16F76B6E04aF9'),
('D', 'ATRI', 'eip155:1/erc20:0xdacD69347dE42baBfAEcD09dC88958378780FB62'),
('D', 'AUDT', 'eip155:1/erc20:******************************************'),
('D', 'B2M', 'eip155:1/erc20:******************************************'),
('D', 'BADGER', 'eip155:1/erc20:******************************************'),
('D', 'BIFI', 'eip155:56/erc20:******************************************'),
('D', 'TUSD', 'eip155:1/erc20:******************************************'),
('D', 'RAMP', 'eip155:1/erc20:******************************************'),
('D', 'RENBTC', 'eip155:1/erc20:******************************************'),
('D', 'CEL', 'eip155:1/erc20:******************************************'),
('D', 'DNT', 'eip155:1/erc20:******************************************'),
('D', 'FXS', 'eip155:1/erc20:******************************************'),
('D', 'GNO', 'eip155:1/erc20:******************************************'),
('D', 'IOTX', 'eip155:1/erc20:******************************************'),
('D', 'O3', 'eip155:1/erc20:******************************************'),
('D', 'ONSTON', 'eip155:1/erc20:******************************************'),
('D', 'PROS', 'eip155:1/erc20:******************************************'),
('D', 'PYR', 'eip155:1/erc20:******************************************'),
('D', 'REVV', 'eip155:1/erc20:******************************************'),
('D', 'RGT', 'eip155:1/erc20:******************************************'),
('D', 'RNDR', 'eip155:1/erc20:******************************************'),
('D', 'SXP', 'eip155:1/erc20:******************************************'),
('D', 'VEE', 'eip155:1/erc20:0x340D2bdE5Eb28c1eed91B2f790723E3B160613B7'),
('D', 'VITE', 'eip155:1/erc20:******************************************'),
('D', 'VSP', 'eip155:1/erc20:0x1b40183EFB4Dd766f11bDa7A7c3AD8982e998421'),
('D', 'XCAD', 'eip155:1/erc20:0x7659CE147D0e714454073a5dd7003544234b6Aa0'),
('D', 'ZIL', 'eip155:1/erc20:******************************************'),
('D', 'BONDLY', 'eip155:1/erc20:0xD2dDa223b2617cB616c1580db421e4cFAe6a8a85'),
('D', 'BTBS', 'eip155:1/erc20:0x32E6C34Cd57087aBBD59B5A4AECC4cB495924356'),
('D', 'BTU', 'eip155:1/erc20:0xb683D83a532e2Cb7DFa5275eED3698436371cc9f'),
('D', 'CIV', 'eip155:1/erc20:******************************************'),
('D', 'CMCX', 'eip155:1/erc20:0x5B685863494C33F344081F75e5430C260c224A32'),
('D', 'COT', 'eip155:1/erc20:0x5CAc718A3AE330d361e39244BF9e67AB17514CE8'),
('D', 'DEP', 'eip155:1/erc20:******************************************'),
('D', 'FEVR', 'eip155:56/erc20:0x82030CDBD9e4B7c5bb0b811A61DA6360D69449cc'),
('D', 'LAND', 'eip155:1/erc20:0x0e2ef8AeCB3c01Ad5D596f1B67134e178199984D'),
('D', 'YLD', 'eip155:1/erc20:0xF94b5C5651c888d928439aB6514B93944eEE6F48'),
('D', 'WEC', 'eip155:1/erc20:0xcC1a8BD438BebC4b2a885a34475BB974f2124317'),
('D', 'VVT', 'eip155:1/erc20:0x755be920943eA95e39eE2DC437b268917B580D6e'),
('D', 'VEMP', 'eip155:1/erc20:0xcFEB09C3c5F0f78aD72166D55f9e6E9A60e96eEC'),
('D', 'URQA', 'eip155:1/erc20:0x1735Db6AB5BAa19eA55d0AdcEeD7bcDc008B3136'),
('D', 'UNIX', 'eip155:1/erc20:0xDDD6A0ECc3c6F6C102E5eA3d8Af7B801d1a77aC8'),
('D', 'UBT', 'eip155:1/erc20:0x8400D94A5cb0fa0D041a3788e395285d61c9ee5e'),
('D', 'TRZ', 'eip155:1/erc20:0x394A16744Dcd805BB0cA7252e70691f0dcAc56AA'),
('D', 'TRYB', 'eip155:1/erc20:0x2C537E5624e4af88A7ae4060C022609376C8D0EB'),
('D', 'TLM', 'eip155:1/erc20:0x888888848B652B3E3a0f34c96E00EEC0F3a23F72'),
('D', 'SHR', 'eip155:1/erc20:0xd98F75b1A3261dab9eEd4956c93F33749027a964'),
('D', 'RNB', 'eip155:1/erc20:0x2A039B1D9bBDccBb91be28691b730ca893e5e743'),
('D', 'RFOX', 'eip155:1/erc20:0xa1d6Df714F91DeBF4e0802A542E13067f31b8262'),
('D', 'PRT', 'eip155:1/erc20:0x6D0F5149c502faf215C89ab306ec3E50b15e2892'),
('D', 'PPAY', 'eip155:1/erc20:0x054D64b73d3D8A21Af3D764eFd76bCaA774f3Bb2'),
('D', 'POLC', 'eip155:1/erc20:0xaA8330FB2B4D5D07ABFE7A72262752a8505C6B37'),
('D', 'PMA', 'eip155:1/erc20:0x846C66cf71C43f80403B51fE3906B3599D63336f'),
('D', 'PKR', 'eip155:1/erc20:0x001A8Ffcb0f03e99141652eBCdecDb0384E3bd6c'),
('D', 'ORBS', 'eip155:1/erc20:******************************************'),
('D', 'MYST', 'eip155:1/erc20:0xa645264C5603E96c3b0B078cdab68733794B0A71'),
('D', 'MARS4', 'eip155:1/erc20:******************************************'),
('D', 'LBL', 'eip155:1/erc20:0x2162f572B25f7358db9376AB58a947a4e45CeDE1'),
('D', 'KAI', 'eip155:1/erc20:0xD9Ec3ff1f8be459Bb9369b4E79e9Ebcf7141C093'),
('D', 'JGN', 'eip155:1/erc20:0x73374Ea518De7adDD4c2B624C0e8B113955ee041'),
('D', 'HYVE', 'eip155:1/erc20:0xd794DD1CAda4cf79C9EebaAb8327a1B0507ef7d4'),
('D', 'HYDRO', 'eip155:1/erc20:0xEBBdf302c940c6bfd49C6b165f457fdb324649bc'),
('D', 'HOTCROSS', 'eip155:1/erc20:0x4297394c20800E8a38A619A243E9BbE7681Ff24E'),
('D', 'FUSE', 'eip155:1/erc20:0x970B9bB2C0444F5E81e9d0eFb84C8ccdcdcAf84d'),
('D', 'BIST', 'eip155:1/erc20:0x6e8908cfa881C9f6f2C64d3436E7b80b1bf0093F'),
('D', 'DMR', 'eip155:1/erc20:0xF74941046389c78F12fE44784C0EC9ca7CEb7dc2'),
('D', 'DRC', 'eip155:1/erc20:0xa150Db9b1Fa65b44799d4dD949D922c0a33Ee606'),
('D', 'EQX', 'eip155:1/erc20:0xBd3de9a069648c84d27d74d701C9fa3253098B15'),
('D', 'FOR', 'eip155:1/erc20:******************************************'),
('D', 'SPI', 'eip155:1/erc20:0x9B02dD390a603Add5c07f9fd9175b7DABE8D63B7'),
('D', 'INV', 'eip155:1/erc20:0x41D5D79431A913C4aE7d69a668ecdfE5fF9DFB68'),
('D', 'MIM', 'eip155:1/erc20:0x99D8a9C45b2ecA8864373A26D1459e3Dff1e17F3'),
('D', 'METADIUM', 'META'),
('D', 'MUNT', 'NLG'),
('D', 'ZUSD', 'eip155:1/erc20:0xc56c2b7e71B54d38Aab6d52E94a04Cbfa8F604fA'),
('D', 'RSR', 'eip155:1/erc20:******************************************'),
('D', 'STSW', 'eip155:1/erc20:0x1C98B54d673C026C8286bADCa3E840aaf72931a3'),
('D', 'TON', 'eip155:1/erc20:******************************************'),
('D', 'DOV', 'eip155:1/erc20:0xac3211a5025414Af2866FF09c23FC18bc97e79b1'),
('D', 'WIN', 'WIN-3'),
('\', 'BNB', 'eip155:1/erc20:0xB8c77482e45F1F44dE1745F52C74426C631bDD52'),
('\', 'FTX', 'eip155:1/erc20:0xd559f20296FF4895da39b5bd9ADd54b442596a61'),
('\', 'ENJ', 'eip155:1/erc20:******************************************'),
('\', 'BUSD', 'eip155:1/erc20:******************************************'),
('\', 'PAX', 'eip155:1/erc20:0x1456688345527bE1f37E9e627DA0837D6f08C925'),
('m', 'APE', 'eip155:1/erc20:0x4d224452801ACEd8B2F0aebE155379bb5D594381'),
('m', 'PEPE', 'eip155:1/erc20:******************************************'),
('m', 'TON', 'eip155:1/erc20:******************************************'),
('m', 'INJ', 'eip155:1/erc20:******************************************'),
('m', 'VELO', 'eip155:56/erc20:******************************************'),
('m', 'PENDLE', 'eip155:1/erc20:******************************************'),
('m', 'TUSD', 'eip155:1/erc20:******************************************'),
('m', 'ACH', 'eip155:1/erc20:******************************************'),
('m', 'LOOKS', 'eip155:1/erc20:0xf4d2888d29D722226FafA5d9B24F9164c092421E'),
('m', 'SFUND', 'eip155:56/erc20:0x477bC8d23c634C154061869478bce96BE6045D12'),
('m', 'IMX', 'eip155:1/erc20:******************************************'),
('m', 'PSP', 'eip155:1/erc20:0xcAfE001067cDEF266AfB7Eb5A286dCFD277f3dE5'),
('m', 'PERP', 'eip155:1/erc20:******************************************'),
('m', 'AXL', 'eip155:1/erc20:******************************************'),
('m', 'WOO', 'eip155:1/erc20:******************************************'),
('m', 'CYBER', 'eip155:56/erc20:******************************************'),
('m', 'SLP', 'eip155:1/erc20:******************************************'),
('m', 'CEL', 'eip155:1/erc20:******************************************'),
('m', 'FLOKI', 'eip155:1/erc20:******************************************'),
('m', 'MAGIC', 'eip155:1/erc20:******************************************'),
('m', 'ZIL', 'eip155:1/erc20:******************************************'),
('m', 'STETH', 'eip155:1/erc20:******************************************'),
('m', 'THETA', 'eip155:1/erc20:******************************************'),
('m', 'WEMIX', 'WEMIX'),
('m', 'MYRIA', 'MYRIA'),
('m', 'NEXT', 'eip155:1/erc20:******************************************'),
('m', 'RNDR', 'eip155:1/erc20:******************************************'),
('m', 'FITFI', 'eip155:43114/erc20:******************************************'),
('m', 'QMALL', 'eip155:1/erc20:******************************************'),
('m', 'ZIG', 'eip155:1/erc20:******************************************'),
('m', 'APEX', 'eip155:1/erc20:******************************************'),
('m', 'FTT', 'eip155:1/erc20:******************************************'),
('m', 'LADYS', 'eip155:1/erc20:******************************************'),
('m', 'ANKR', 'eip155:1/erc20:******************************************'),
('m', 'LENDS', 'eip155:1/erc20:******************************************'),
('m', 'SQR', 'eip155:56/erc20:0x2B72867c32CF673F7b02d208B26889fEd353B1f8'),
('m', 'USDY', 'eip155:1/erc20:0x96F6eF951840721AdBF46Ac996b59E0235CB985C'),
('m', 'PRIME', 'eip155:1/erc20:0xb23d80f5FefcDDaa212212F028021B41DEd428CF'),
('m', 'WAVES', 'eip155:1/erc20:0x1cF4592ebfFd730c7dc92c1bdFFDfc3B9EfCf29a'),
('m', 'CTC', 'eip155:1/erc20:******************************************'),
('m', 'MELOS', 'eip155:1/erc20:0x1afb69DBC9f54d08DAB1bD3436F8Da1af819E647'),
('m', 'MDAO', 'eip155:56/erc20:0x60322971a672B81BccE5947706D22c19dAeCf6Fb'),
('m', 'TIME', 'eip155:1/erc20:0x6531f133e6DeeBe7F2dcE5A0441aA7ef330B4e53'),
('m', 'DSRUN', 'eip155:137/erc20:0xFf76c0B48363A7C7307868a81548d340049b0023'),
('m', 'FET', 'eip155:1/erc20:******************************************'),
('m', 'AIOZ', 'eip155:1/erc20:******************************************'),
('m', 'PUMLX', 'eip155:1/erc20:0x8c088775e4139af116Ac1FA6f281Bbf71E8c1c73'),
('m', 'PURSE', 'eip155:56/erc20:0x29a63F4B209C29B4DC47f06FFA896F32667DAD2C'),
('m', 'SEILOR', 'eip155:56/erc20:0xE29142E14E52bdFBb8108076f66f49661F10EC10'),
('m', 'KARATE', 'eip155:56/erc20:0xAcf79C09Fff518EcBe2A96A2c4dA65B68fEDF6D3'),
('m', 'PSTAKE', 'eip155:1/erc20:0xfB5c6815cA3AC72Ce9F5006869AE67f18bF77006'),
('m', 'PLANET', 'eip155:1/erc20:0x2aD9adDD0d97EC3cDBA27F92bF6077893b76Ab0b'),
('m', 'PEOPLE', 'eip155:1/erc20:0x7A58c0Be72BE218B41C608b7Fe7C5bB630736C71'),
('m', 'TURBOS', 'eip155:1/erc20:0x0678Ca162E737C44cab2Ea31b4bbA78482E1313d'),
('m', 'TRVL', 'eip155:1/erc20:0xd47bDF574B4F76210ed503e0EFe81B58Aa061F3d'),
('m', 'VELA', 'eip155:42161/erc20:0x088cd8f5eF3652623c22D48b1605DCfE860Cd704'),
('m', 'INTER', 'INTER'),
('m', 'TRIBE', 'eip155:1/erc20:0xc7283b66Eb1EB5FB86327f08e1B5816b0720212B'),
('m', 'STG', 'eip155:1/erc20:******************************************'),
('m', 'MASK', 'eip155:1/erc20:******************************************'),
('m', 'JASMY', 'eip155:1/erc20:0x7420B4b9a0110cdC71fB720908340C03F9Bc03EC'),
('m', 'DPX', 'eip155:1/erc20:0xEec2bE5c91ae7f8a338e1e5f3b5DE49d07AfdC81'),
('m', 'STRM', 'eip155:1/erc20:0x94a7f270cd12545A277E656266Aef5e27dF3Eb28'),
('m', 'PRIMAL', 'eip155:1/erc20:0xDd13DEdeCEbDA566322195bc4451D672A148752C'),
('m', 'KUNCI', 'eip155:56/erc20:0x6cf271270662be1C4fc1b7BB7D7D7Fc60Cc19125'),
('m', 'MAVIA', 'eip155:1/erc20:0x24fcFC492C1393274B6bcd568ac9e225BEc93584'),
('m', 'TENET', 'eip155:1/erc20:0x9663677B81c2D427E81C01ef7315eA96546F5Bb1'),
('m', 'SHILL', 'SHILL'),
('m', 'MOVEZ', 'eip155:56/erc20:0x039cD22cb49084142d55FCD4B6096A4F51ffb3B4'),
('m', 'FXS', 'eip155:1/erc20:******************************************'),
('m', 'LMWR', 'eip155:1/erc20:******************************************'),
('m', 'SHRAP', 'eip155:1/erc20:0x31e4efe290973ebE91b3a875a7994f650942D28F'),
('m', 'VEXT', 'eip155:1/erc20:******************************************'),
('m', 'SIDUS', 'eip155:1/erc20:0x549020a9Cb845220D66d3E9c6D9F9eF61C981102'),
('m', 'ZETA', 'eip155:1/erc20:******************************************'),
('m', 'C98', 'eip155:1/erc20:******************************************'),
('m', 'EVER', 'eip155:1/erc20:0x29d578CEc46B50Fa5C88a99C6A4B70184C062953'),
('m', 'APP', 'eip155:1/erc20:******************************************'),
('K', 'TRB', 'eip155:1/erc20:******************************************'),
('K', 'GTC', 'eip155:1/erc20:******************************************'),
('K', 'FET', 'eip155:1/erc20:******************************************'),
('K', 'WLUNA', 'eip155:1/erc20:******************************************'),
('K', 'BOND', 'eip155:1/erc20:0x0391D2021f89DC339F60Fff84546EA23E337750f'),
('K', 'ASM', 'eip155:1/erc20:0x2565ae0385659badCada1031DB704442E1b69982'),
('K', 'IMX', 'eip155:1/erc20:******************************************'),
('K', 'TRU', 'eip155:1/erc20:******************************************'),
('K', 'FARM', 'eip155:1/erc20:******************************************'),
('K', 'STX', 'STX-2'),
('K', 'NCT', 'eip155:1/erc20:******************************************'),
('K', 'MLN', 'eip155:1/erc20:******************************************'),
('K', 'REP', 'eip155:1/erc20:******************************************'),
('K', 'DEXT', 'eip155:1/erc20:******************************************'),
('K', 'ALEPH', 'eip155:1/erc20:0x27702a26126e0B3702af63Ee09aC4d1A084EF628'),
('K', 'PRQ', 'eip155:1/erc20:******************************************'),
('K', 'ORN', 'eip155:1/erc20:******************************************'),
('K', 'KNC', 'eip155:1/erc20:0xdeFA4e8a7bcBA345F687a2f1456F5Edd9CE97202'),
('K', 'INV', 'eip155:1/erc20:0xEcE83617Db208Ad255Ad4f45Daf81E25137535bb'),
('K', 'ANT', 'eip155:1/erc20:******************************************'),
('K', 'INJ', 'eip155:1/erc20:******************************************'),
('K', 'CTSI', 'eip155:1/erc20:******************************************'),
('K', 'BADGER', 'eip155:1/erc20:******************************************'),
('K', 'QUICK', 'eip155:1/erc20:******************************************'),
('K', 'RARE', 'eip155:1/erc20:0xba5BDe662c17e2aDFF1075610382B9B691296350'),
('K', 'FOX', 'eip155:1/erc20:0xc770EEfAd204B5180dF6a14Ee197D99d808ee52d'),
('K', 'ANKR', 'eip155:1/erc20:******************************************'),
('K', 'TIME', 'eip155:1/erc20:0x6531f133e6DeeBe7F2dcE5A0441aA7ef330B4e53'),
('K', 'METIS', 'eip155:1/erc20:******************************************'),
('K', 'HOPR', 'eip155:1/erc20:0xF5581dFeFD8Fb0e4aeC526bE659CFaB1f8c781dA'),
('K', 'GNO', 'eip155:1/erc20:******************************************'),
('K', 'BUSD', 'eip155:1/erc20:******************************************'),
('K', 'REQ', 'eip155:1/erc20:******************************************'),
('K', 'RAI', 'eip155:1/erc20:0x03ab458634910AaD20eF5f1C8ee96F1D6ac54919'),
('K', 'RNDR', 'eip155:1/erc20:******************************************'),
('K', 'INDEX', 'eip155:1/erc20:******************************************'),
('K', 'DYP', 'eip155:1/erc20:******************************************'),
('K', 'MATH', 'eip155:1/erc20:0x08d967bb0134F2d07f7cfb6E246680c53927DD30'),
('K', 'CLV', 'eip155:1/erc20:******************************************'),
('K', 'SUPER', 'eip155:1/erc20:******************************************'),
('K', 'PERP', 'eip155:1/erc20:******************************************'),
('K', 'AIOZ', 'eip155:1/erc20:******************************************'),
('K', 'STG', 'eip155:1/erc20:******************************************'),
('K', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('K', 'ARPA', 'eip155:1/erc20:******************************************'),
('K', 'DNT', 'eip155:1/erc20:******************************************'),
('K', 'CELR', 'eip155:1/erc20:******************************************'),
('K', 'HIGH', 'eip155:1/erc20:******************************************'),
('K', 'POLS', 'eip155:1/erc20:******************************************'),
('K', 'C98', 'eip155:1/erc20:******************************************'),
('K', 'ACH', 'eip155:1/erc20:******************************************'),
('K', 'MIR', 'eip155:1/erc20:******************************************'),
('K', 'MASK', 'eip155:1/erc20:******************************************'),
('K', 'IOTX', 'eip155:1/erc20:******************************************'),
('K', 'DIA', 'eip155:1/erc20:******************************************'),
('K', 'BLZ', 'eip155:1/erc20:******************************************'),
('K', 'RGT', 'eip155:1/erc20:******************************************'),
('K', 'ATA', 'eip155:1/erc20:0xA2120b9e674d3fC3875f415A7DF52e382F141225'),
('K', 'NEST', 'eip155:1/erc20:******************************************'),
('K', 'SYN', 'eip155:1/erc20:******************************************'),
('K', 'GHST', 'eip155:1/erc20:******************************************'),
('K', 'HFT', 'eip155:1/erc20:******************************************'),
('K', 'AXL', 'eip155:1/erc20:******************************************'),
('K', 'MAGIC', 'eip155:1/erc20:******************************************'),
('K', 'PYR', 'eip155:1/erc20:******************************************'),
('K', 'DIMO', 'eip155:1/erc20:******************************************'),
('K', 'LSETH', 'eip155:1/erc20:******************************************'),
('K', 'MULTI', 'eip155:1/erc20:******************************************'),
('K', 'ALCX', 'eip155:1/erc20:******************************************'),
('K', 'SHIB', 'eip155:1/erc20:******************************************'),
('K', 'LOOM', 'eip155:1/erc20:******************************************'),
('K', 'AST', 'eip155:1/erc20:******************************************'),
('K', 'AVT', 'eip155:1/erc20:******************************************'),
('K', 'CRO', 'eip155:1/erc20:******************************************'),
('K', 'ERN', 'eip155:1/erc20:******************************************'),
('K', 'FIS', 'eip155:1/erc20:******************************************'),
('K', 'QI', 'eip155:43114/erc20:******************************************'),
('K', 'SEAM', 'eip155:1/erc20:******************************************'),
('K', 'CBETH', 'eip155:1/erc20:******************************************'),
('K', 'VELO', 'eip155:10/erc20:******************************************'),
('K', 'ZETA', 'eip155:1/erc20:******************************************'),
('K', 'RENDER', 'RENDER'),
('G', 'AGLD', 'eip155:1/erc20:******************************************'),
('G', 'FET', 'eip155:1/erc20:******************************************'),
('G', 'GTC', 'eip155:1/erc20:******************************************'),
('G', 'TRU', 'eip155:1/erc20:******************************************'),
('G', 'FARM', 'eip155:1/erc20:******************************************'),
('G', 'STX', 'STX-2'),
('G', 'MLN', 'eip155:1/erc20:******************************************'),
('G', 'REP', 'eip155:1/erc20:******************************************'),
('G', 'PRQ', 'eip155:1/erc20:******************************************'),
('G', 'ORN', 'eip155:1/erc20:******************************************'),
('G', 'KNC', 'eip155:1/erc20:0xdeFA4e8a7bcBA345F687a2f1456F5Edd9CE97202'),
('G', 'INV', 'eip155:1/erc20:0x41D5D79431A913C4aE7d69a668ecdfE5fF9DFB68'),
('G', 'BUSD', 'eip155:1/erc20:******************************************'),
('G', 'BADGER', 'eip155:1/erc20:******************************************'),
('G', 'FOX', 'eip155:1/erc20:0xc770EEfAd204B5180dF6a14Ee197D99d808ee52d'),
('G', 'QUICK', 'eip155:1/erc20:******************************************'),
('G', 'HOPR', 'eip155:1/erc20:0xF5581dFeFD8Fb0e4aeC526bE659CFaB1f8c781dA'),
('G', 'RAI', 'eip155:1/erc20:0x03ab458634910AaD20eF5f1C8ee96F1D6ac54919'),
('G', 'INJ', 'eip155:1/erc20:******************************************'),
('G', 'GNO', 'eip155:1/erc20:******************************************'),
('G', 'METIS', 'eip155:1/erc20:******************************************'),
('G', 'MIR', 'eip155:1/erc20:******************************************'),
('G', 'TIME', 'eip155:1/erc20:0x6531f133e6DeeBe7F2dcE5A0441aA7ef330B4e53'),
('G', 'STG', 'eip155:1/erc20:******************************************'),
('G', 'GHST', 'eip155:1/erc20:******************************************'),
('G', 'MATH', 'eip155:1/erc20:0x08d967bb0134F2d07f7cfb6E246680c53927DD30'),
('G', 'RARE', 'eip155:1/erc20:0xba5BDe662c17e2aDFF1075610382B9B691296350'),
('G', 'AXL', 'eip155:1/erc20:******************************************'),
('G', 'SUPER', 'eip155:1/erc20:******************************************'),
('G', 'ALEPH', 'eip155:1/erc20:0x27702a26126e0B3702af63Ee09aC4d1A084EF628'),
('G', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('G', 'IMX', 'eip155:1/erc20:******************************************'),
('G', 'C98', 'eip155:1/erc20:******************************************'),
('G', 'CLV', 'eip155:1/erc20:******************************************'),
('G', 'WLUNA', 'eip155:1/erc20:******************************************'),
('G', 'REQ', 'eip155:1/erc20:******************************************'),
('G', 'PERP', 'eip155:1/erc20:******************************************'),
('G', 'MASK', 'eip155:1/erc20:******************************************'),
('G', 'TBTC', 'eip155:1/erc20:******************************************'),
('G', 'ANT', 'eip155:1/erc20:******************************************'),
('G', 'INDEX', 'eip155:1/erc20:******************************************'),
('G', 'ACH', 'eip155:1/erc20:******************************************'),
('G', 'MAGIC', 'eip155:42161/erc20:******************************************'),
('G', 'NEST', 'eip155:1/erc20:******************************************'),
('G', 'CTSI', 'eip155:1/erc20:******************************************'),
('G', 'SYN', 'eip155:1/erc20:******************************************'),
('G', 'AIOZ', 'eip155:1/erc20:******************************************'),
('G', 'RGT', 'eip155:1/erc20:******************************************'),
('G', 'ARPA', 'eip155:1/erc20:******************************************'),
('G', 'ANKR', 'eip155:1/erc20:******************************************'),
('G', 'DYP', 'eip155:1/erc20:******************************************'),
('G', 'POLS', 'eip155:1/erc20:******************************************'),
('G', 'HFT', 'eip155:1/erc20:******************************************'),
('G', 'ATA', 'eip155:1/erc20:0xA2120b9e674d3fC3875f415A7DF52e382F141225'),
('G', 'IOTX', 'eip155:1/erc20:******************************************'),
('G', 'HIGH', 'eip155:1/erc20:******************************************'),
('G', 'TRB', 'eip155:1/erc20:0x88df592f8eb5d7bd38bfef7deb0fbc02cf3778a0'),
('G', 'RNDR', 'eip155:1/erc20:******************************************'),
('G', 'DIA', 'eip155:56/erc20:0x99956D38059cf7bEDA96Ec91Aa7BB2477E0901DD'),
('G', 'CELR', 'eip155:1/erc20:******************************************'),
('G', 'PYR', 'eip155:1/erc20:******************************************'),
('G', 'DEXT', 'eip155:1/erc20:******************************************'),
('G', 'DNT', 'eip155:1/erc20:******************************************'),
('G', 'NCT', 'eip155:1/erc20:******************************************'),
('G', 'BLZ', 'eip155:1/erc20:******************************************'),
('G', 'DIMO', 'eip155:1/erc20:******************************************'),
('G', 'LSETH', 'eip155:1/erc20:******************************************'),
('G', 'MULTI', 'eip155:1/erc20:******************************************'),
('G', 'QI', 'eip155:43114/erc20:******************************************'),
('G', 'AVT', 'eip155:1/erc20:******************************************'),
('G', 'SEAM', 'eip155:1/erc20:******************************************'),
('G', 'AST', 'eip155:1/erc20:******************************************'),
('G', 'ERN', 'eip155:1/erc20:******************************************'),
('G', 'FIS', 'eip155:1/erc20:******************************************'),
('G', 'CBETH', 'eip155:1/erc20:******************************************'),
('G', 'VELO', 'eip155:10/erc20:******************************************'),
('G', 'ZETA', 'eip155:1/erc20:******************************************'),
('G', 'RENDER', 'RENDER'),
('Z', 'FTT', 'eip155:1/erc20:******************************************'),
('Z', 'ASD', 'eip155:1/erc20:******************************************'),
('Z', 'COIN', 'COIN'),
('Z', 'SLP', 'eip155:1/erc20:******************************************'),
('Z', 'MER', 'MER-2'),
('Z', 'SRM', 'eip155:1/erc20:******************************************'),
('Z', 'IMX', 'eip155:1/erc20:******************************************'),
('Z', 'GENE', 'GENE'),
('Z', 'EURT', 'eip155:1/erc20:******************************************'),
('Z', 'INDI', 'eip155:1/erc20:******************************************'),
('Z', 'STG', 'eip155:1/erc20:******************************************'),
('Z', 'WFLOW', 'eip155:1/erc20:******************************************'),
('Z', 'ALEPH', 'eip155:1/erc20:0x27702a26126e0B3702af63Ee09aC4d1A084EF628'),
('Z', 'SYN', 'eip155:1/erc20:******************************************'),
('Z', 'MATH', 'eip155:1/erc20:0x08d967bb0134F2d07f7cfb6E246680c53927DD30'),
('Z', 'PERP', 'eip155:1/erc20:******************************************'),
('Z', 'SXP', 'eip155:1/erc20:******************************************'),
('Z', 'FXS', 'eip155:1/erc20:******************************************'),
('Z', 'CREAM', 'eip155:1/erc20:0x2ba592F78dB6436527729929AAf6c908497cB200'),
('Z', 'ALPHA', 'eip155:1/erc20:******************************************'),
('Z', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('Z', 'BADGER', 'eip155:1/erc20:******************************************'),
('Z', 'C98', 'eip155:1/erc20:******************************************'),
('Z', 'WRX', 'eip155:56/erc20:0x8e17ed70334C87eCE574C9d537BC153d8609e2a3'),
('Z', 'AMPL', 'eip155:1/erc20:0xD46bA6D942050d489DBd938a2C909A5d5039A161'),
('Z', 'MTA', 'eip155:1/erc20:0xa3BeD4E1c75D00fa6f4E5E6922DB7261B5E9AcD2'),
('Z', 'DODO', 'eip155:1/erc20:******************************************'),
('Z', 'FRONT', 'eip155:1/erc20:******************************************'),
('Z', 'HMT', 'eip155:1/erc20:0xd1ba9BAC957322D6e8c07a160a3A8dA11A0d2867'),
('Z', 'LINA', 'eip155:1/erc20:******************************************'),
('Z', 'MCB', 'eip155:1/erc20:******************************************'),
('Z', 'ORBS', 'eip155:1/erc20:******************************************'),
('Z', 'PROM', 'eip155:1/erc20:******************************************'),
('Z', 'REEF', 'eip155:1/erc20:******************************************'),
('Z', 'RNDR', 'eip155:1/erc20:******************************************'),
('Z', 'TLM', 'eip155:1/erc20:0x888888848B652B3E3a0f34c96E00EEC0F3a23F72'),
('Z', 'TONCOIN', 'eip155:1/erc20:******************************************'),
('Z', 'TRYB', 'eip155:1/erc20:0x2C537E5624e4af88A7ae4060C022609376C8D0EB'),
('Z', 'UBXT', 'eip155:1/erc20:0x8564653879a18C560E7C0Ea0E084c516C62F5653'),
('Z', 'CHR', 'eip155:1/erc20:0x915044526758533dfB918ecEb6e44bc21632060D'),
('Z', 'CLV', 'eip155:1/erc20:******************************************'),
('Z', 'CEL', 'eip155:1/erc20:******************************************'),
('Z', 'MAGIC', 'eip155:1/erc20:******************************************'),
('Z', 'MYC', 'eip155:1/erc20:0x4b13006980aCB09645131b91D259eaA111eaF5Ba'),
('Z', 'RSR', 'eip155:1/erc20:******************************************'),
('L', 'SLP', 'eip155:1/erc20:******************************************'),
('L', 'RARE', 'eip155:1/erc20:0xba5BDe662c17e2aDFF1075610382B9B691296350'),
('L', 'AUDIO', 'eip155:1/erc20:0x18aAA7115705e8be94bfFEBDE57Af9BFc265B998'),
('L', 'FET', 'eip155:1/erc20:******************************************'),
('L', 'LUNA', 'eip155:1/erc20:******************************************'),
('L', 'METIS', 'eip155:1/erc20:******************************************'),
('L', 'FXS', 'eip155:1/erc20:******************************************'),
('L', 'INDEX', 'eip155:1/erc20:******************************************'),
('L', 'INJ', 'eip155:1/erc20:******************************************'),
('L', 'MIM', 'eip155:1/erc20:0x99D8a9C45b2ecA8864373A26D1459e3Dff1e17F3'),
('L', 'MC', 'eip155:1/erc20:******************************************'),
('L', 'MIR', 'eip155:1/erc20:******************************************'),
('L', 'IMX', 'eip155:1/erc20:******************************************'),
('L', 'REVV', 'eip155:1/erc20:******************************************'),
('L', 'MASK', 'eip155:1/erc20:******************************************'),
('L', 'ELON', 'eip155:1/erc20:******************************************'),
('L', 'DPI', 'eip155:1/erc20:0x1494CA1F11D487c2bBe4543E90080AeBa4BA3C2b'),
('L', 'FRAX', 'eip155:1/erc20:******************************************'),
('L', 'ANKR', 'eip155:1/erc20:******************************************'),
('L', 'LUSD', 'eip155:1/erc20:******************************************'),
('L', 'IOTX', 'eip155:1/erc20:******************************************'),
('L', 'RNDR', 'eip155:1/erc20:******************************************'),
('L', 'BUSD', 'eip155:1/erc20:******************************************'),
('L', 'PEPE', 'eip155:1/erc20:******************************************'),
('L', 'ERN', 'eip155:1/erc20:******************************************'),
('L', 'ALI', 'eip155:1/erc20:******************************************'),
('V', 'ADX', 'eip155:1/erc20:******************************************'),
('V', 'ANT', 'eip155:1/erc20:******************************************'),
('V', 'REP', 'eip155:1/erc20:******************************************'),
('V', 'FTT', 'eip155:1/erc20:******************************************'),
('V', 'HOT', 'eip155:1/erc20:******************************************'),
('V', 'PNT', 'eip155:1/erc20:******************************************'),
('V', 'FET', 'eip155:1/erc20:******************************************'),
('V', 'TRB', 'eip155:1/erc20:******************************************'),
('V', 'EDG', 'eip155:1/erc20:0x08711D3B02C8758F2FB3ab4e80228418a7F8e39c'),
('V', 'RCN', 'eip155:1/erc20:******************************************'),
('V', 'ONE', 'ONE-2'),
('V', 'FXS', 'eip155:1/erc20:******************************************'),
('V', 'BADGER', 'eip155:1/erc20:******************************************'),
('V', 'ANKR', 'eip155:1/erc20:******************************************'),
('V', 'REEF', 'eip155:1/erc20:******************************************'),
('V', 'REQ', 'eip155:1/erc20:******************************************'),
('V', 'MIR', 'eip155:1/erc20:******************************************'),
('V', 'INJ', 'eip155:1/erc20:******************************************'),
('V', 'ZIL', 'eip155:1/erc20:******************************************'),
('V', 'SXP', 'eip155:1/erc20:******************************************'),
('V', 'TUSD', 'eip155:1/erc20:******************************************'),
('V', 'CHR', 'eip155:1/erc20:0x915044526758533dfB918ecEb6e44bc21632060D'),
('V', 'FRONT', 'eip155:1/erc20:******************************************'),
('V', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('V', 'ALPHA', 'eip155:1/erc20:******************************************'),
('V', 'BLZ', 'eip155:1/erc20:******************************************'),
('V', 'DNT', 'eip155:1/erc20:******************************************'),
('V', 'GNO', 'eip155:1/erc20:******************************************'),
('V', 'LINA', 'eip155:1/erc20:******************************************'),
('V', 'RNDR', 'eip155:1/erc20:******************************************'),
('V', 'ALPACA', 'eip155:56/erc20:******************************************'),
('V', 'BUSD', 'eip155:1/erc20:******************************************'),
('V', 'RSR', 'eip155:1/erc20:******************************************'),
('V', 'AST', 'eip155:1/erc20:******************************************'),
('V', 'IMX', 'eip155:1/erc20:******************************************'),
('V', 'MAGIC', 'eip155:1/erc20:******************************************'),
('B', 'ATOM', 'ATOM'),
('B', 'ALGO', 'ALGO'),
('B', 'ZAUD', 'AUD'),
('B', 'DOT', 'DOT'),
('B', 'KAVA', 'KAVA'),
('B', 'BSV', 'BSV'),
('B', 'XETC', 'ETC'),
('B', 'XETH', 'ETH'),
('B', 'XLTC', 'LTC'),
('B', 'XREP', 'eip155:1/erc20:******************************************'),
('B', 'XXBT', 'BTC'),
('B', 'XXMR', 'XMR'),
('B', 'XXRP', 'XRP'),
('B', 'XZEC', 'ZEC'),
('B', 'ZEUR', 'EUR'),
('B', 'ZUSD', 'USD'),
('B', 'ZGBP', 'GBP'),
('B', 'ZCAD', 'CAD'),
('B', 'ZJPY', 'JPY'),
('B', 'CHF', 'CHF'),
('B', 'ZKRW', 'KRW'),
('B', 'AED', 'AED'),
('B', 'REPV2', 'eip155:1/erc20:******************************************'),
('B', 'XDAO', 'DAO'),
('B', 'XMLN', 'eip155:1/erc20:******************************************'),
('B', 'XICN', 'ICN'),
('B', 'GNO', 'eip155:1/erc20:******************************************'),
('B', 'BCH', 'BCH'),
('B', 'XXLM', 'XLM'),
('B', 'DASH', 'DASH'),
('B', 'EOS', 'EOS'),
('B', 'KFEE', 'KFEE'),
('B', 'ADA', 'ADA'),
('B', 'QTUM', 'QTUM'),
('B', 'XNMC', 'NMC'),
('B', 'XXVN', 'VEN'),
('B', 'XXDG', 'DOGE'),
('B', 'XTZ', 'XTZ'),
('B', 'WAVES', 'WAVES'),
('B', 'ICX', 'ICX'),
('B', 'NANO', 'NANO'),
('B', 'OMG', 'OMG'),
('B', 'SC', 'SC'),
('B', 'PAXG', 'PAXG'),
('B', 'LSK', 'LSK'),
('B', 'TRX', 'TRX'),
('B', 'OXT', 'OXT'),
('B', 'STORJ', 'STORJ'),
('B', 'KSM', 'KSM'),
('B', 'FIL', 'FIL'),
('B', 'ANT', 'eip155:1/erc20:******************************************'),
('B', 'KEEP', 'KEEP'),
('B', 'ETH2', 'ETH2'),
('B', 'FLOW', 'FLOW'),
('B', 'OCEAN', 'OCEAN'),
('B', 'EWT', 'EWT'),
('B', 'MKR', 'eip155:1/erc20:******************************************'),
('B', 'RARI', 'eip155:1/erc20:******************************************'),
('B', 'REN', 'REN'),
('B', 'ZRX', 'eip155:1/erc20:******************************************'),
('B', 'GHST', 'eip155:1/erc20:******************************************'),
('B', 'ANKR', 'eip155:1/erc20:******************************************'),
('B', 'LPT', 'eip155:1/erc20:******************************************'),
('B', 'BNT', 'eip155:1/erc20:******************************************'),
('B', 'ENJ', 'eip155:1/erc20:******************************************'),
('B', 'MINA', 'MINA'),
('B', 'SRM', 'SRM'),
('B', 'OGN', 'eip155:1/erc20:******************************************'),
('B', 'PERP', 'eip155:1/erc20:******************************************'),
('B', 'CQT', 'eip155:1/erc20:******************************************'),
('B', 'CTSI', 'eip155:1/erc20:******************************************'),
('B', 'KAR', 'KAR'),
('B', 'BADGER', 'eip155:1/erc20:******************************************'),
('B', 'MIR', 'eip155:1/erc20:******************************************'),
('B', 'BAND', 'eip155:1/erc20:0xBA11D00c5f74255f56a5E366F4F77f5A186d7f55'),
('B', 'INJ', 'eip155:1/erc20:******************************************'),
('B', 'MOVR', 'MOVR'),
('B', 'SDN', 'SDN'),
('B', 'DYDX', 'eip155:1/erc20:0x92D6C1e31e14520e676a687F0a93788B716BEff5'),
('B', 'OXY', 'OXY'),
('B', 'RAY', 'RAY'),
('B', 'PHA', 'eip155:1/erc20:0x6c5bA91642F10282b576d91922Ae6448C9d52f4E'),
('B', 'BNC', 'BNC'),
('B', 'LUNA', 'eip155:1/erc20:******************************************'),
('B', 'SHIB', 'eip155:1/erc20:******************************************'),
('B', 'AVAX', 'AVAX'),
('B', 'KILT', 'KILT'),
('B', 'STEP', 'STEP'),
('B', 'UST', 'UST'),
('B', 'MNGO', 'MNGO'),
('B', 'ORCA', 'ORCA'),
('B', 'KINT', 'KINT'),
('B', 'GLMR', 'GLMR'),
('B', 'ATLAS', 'ATLAS'),
('B', 'ACA', 'ACA'),
('B', 'AIR', 'AIR'),
('B', 'POLIS', 'POLIS'),
('B', 'KIN', 'KIN'),
('B', 'FIDA', 'FIDA'),
('B', 'ASTR', 'ASTR'),
('B', 'AKT', 'AKT'),
('B', 'SGB', 'SGB'),
('B', 'SBR', 'SBR'),
('B', 'FXS', 'eip155:1/erc20:******************************************'),
('B', 'TRIBE', 'eip155:1/erc20:0xc7283b66Eb1EB5FB86327f08e1B5816b0720212B'),
('B', 'CVX', 'eip155:1/erc20:0x4e3FBD56CD56c3e72c1403e103b45Db9da5B9D2B'),
('B', 'ALCX', 'eip155:1/erc20:******************************************'),
('B', 'ENS', 'eip155:1/erc20:0xC18360217D8F7Ab5e7c516566761Ea12Ce7F9D72'),
('B', 'KP3R', 'eip155:1/erc20:0x1cEB5cB57C4D4E2b2433641b95Dd330A33185A44'),
('B', 'IMX', 'eip155:1/erc20:******************************************'),
('B', 'YGG', 'eip155:1/erc20:0x25f8087EAD173b73D6e8B84329989A8eEA16CF73'),
('B', 'RARE', 'eip155:1/erc20:0xba5BDe662c17e2aDFF1075610382B9B691296350'),
('B', 'ICP', 'ICP'),
('B', 'BOND', 'eip155:1/erc20:0x0391D2021f89DC339F60Fff84546EA23E337750f'),
('B', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('B', 'XRT', 'eip155:1/erc20:0x7dE91B204C1C737bcEe6F000AAA6569Cf7061cb7'),
('B', 'AUDIO', 'eip155:1/erc20:0x18aAA7115705e8be94bfFEBDE57Af9BFc265B998'),
('B', 'WOO', 'eip155:1/erc20:******************************************'),
('B', 'JASMY', 'eip155:1/erc20:0x7420B4b9a0110cdC71fB720908340C03F9Bc03EC'),
('B', 'RNDR', 'eip155:1/erc20:******************************************'),
('B', 'QNT', 'eip155:1/erc20:0x4a220E6096B25EADb88358cb44068A3248254675'),
('B', 'PSTAKE', 'eip155:1/erc20:0xfB5c6815cA3AC72Ce9F5006869AE67f18bF77006'),
('B', 'APE', 'eip155:1/erc20:0x4d224452801ACEd8B2F0aebE155379bb5D594381'),
('B', 'MASK', 'eip155:1/erc20:******************************************'),
('B', 'POWR', 'eip155:1/erc20:0x595832F8FC6BF59c85C527fEC3740A1b7a361269'),
('B', 'SCRT', 'SCRT'),
('B', 'UMA', 'eip155:1/erc20:0x04Fa0d235C4abf4BcF4787aF4CF447DE572eF828'),
('B', 'TOKE', 'eip155:1/erc20:0x2e9d63788249371f1DFC918a52f8d799F4a38C94'),
('B', 'MULTI', 'eip155:1/erc20:******************************************'),
('B', 'RBC', 'eip155:1/erc20:******************************************'),
('B', 'PLA', 'eip155:1/erc20:******************************************'),
('B', 'BICO', 'eip155:1/erc20:******************************************'),
('B', 'MC', 'eip155:1/erc20:******************************************'),
('B', 'MSOL', 'MSOL'),
('B', 'SAMO', 'SAMO'),
('B', 'GARI', 'GARI'),
('B', 'GST', 'GST-2'),
('B', 'GMT', 'GMT'),
('B', 'CFG', 'CFG'),
('B', 'WETH', 'eip155:1/erc20:******************************************'),
('B', 'API3', 'eip155:1/erc20:******************************************'),
('B', 'RUNE', 'RUNE'),
('B', 'RLC', 'eip155:1/erc20:******************************************'),
('B', 'AGLD', 'eip155:1/erc20:******************************************'),
('B', 'FET', 'eip155:1/erc20:******************************************'),
('B', 'NYM', 'eip155:1/erc20:******************************************'),
('B', 'NMR', 'eip155:1/erc20:******************************************'),
('B', 'ROOK', 'eip155:1/erc20:******************************************'),
('B', 'CVC', 'eip155:1/erc20:******************************************'),
('B', 'SUPER', 'eip155:1/erc20:******************************************'),
('B', 'RAD', 'eip155:1/erc20:******************************************'),
('B', 'NEAR', 'NEAR'),
('B', 'SXP', 'eip155:1/erc20:******************************************'),
('B', 'LUNA2', 'LUNA-3'),
('B', 'TLM', 'eip155:1/erc20:0x888888848B652B3E3a0f34c96E00EEC0F3a23F72'),
('B', 'DENT', 'eip155:1/erc20:0x3597bfD533a99c9aa083587B074434E61Eb0A258'),
('B', 'GTC', 'eip155:1/erc20:******************************************'),
('B', 'ADX', 'eip155:1/erc20:******************************************'),
('B', 'LCX', 'eip155:1/erc20:0x037A54AaB062628C9Bbae1FDB1583c195585fe41'),
('B', 'TVK', 'eip155:1/erc20:0xd084B83C305daFD76AE3E1b4E1F1fe2eCcCb3988'),
('B', 'IDEX', 'eip155:1/erc20:0xB705268213D593B8FD88d3FDEFF93AFF5CbDcfAE'),
('B', 'BTT', 'BTT'),
('B', 'REQ', 'eip155:1/erc20:******************************************'),
('B', 'CHR', 'eip155:1/erc20:0x915044526758533dfB918ecEb6e44bc21632060D'),
('B', 'FORTH', 'eip155:1/erc20:0x77FbA179C79De5B7653F68b5039Af940AdA60ce0'),
('B', 'FIS', 'eip155:1/erc20:******************************************'),
('B', 'MXC', 'eip155:1/erc20:0x5Ca381bBfb58f0092df149bD3D243b08B9a8386e'),
('B', 'FARM', 'eip155:1/erc20:******************************************'),
('B', 'ACH', 'eip155:1/erc20:******************************************'),
('B', 'MV', 'MV'),
('B', 'EGLD', 'EGLD'),
('B', 'UNFI', 'UNFI'),
('B', 'COTI', 'COTI'),
('B', 'KEY', 'eip155:1/erc20:******************************************'),
('B', 'BIT', 'eip155:1/erc20:******************************************'),
('B', 'INTR', 'INTR'),
('B', 'TEER', 'TEER'),
('B', 'C98', 'eip155:1/erc20:******************************************'),
('B', 'STG', 'eip155:1/erc20:******************************************'),
('B', 'RPL', 'eip155:1/erc20:******************************************'),
('B', 'ETHW', 'ETHW'),
('B', 'BOBA', 'eip155:1/erc20:******************************************'),
('B', 'CSM', 'eip155:1/erc20:******************************************'),
('B', 'GAL', 'eip155:1/erc20:******************************************'),
('B', 'XCN', 'eip155:1/erc20:******************************************'),
('B', 'ARPA', 'eip155:1/erc20:******************************************'),
('B', 'SYN', 'eip155:1/erc20:******************************************'),
('B', 'TRU', 'eip155:1/erc20:******************************************'),
('B', 'ALPHA', 'eip155:1/erc20:******************************************'),
('B', 'POND', 'eip155:1/erc20:******************************************'),
('B', 'BLZ', 'eip155:1/erc20:******************************************'),
('B', 'CELR', 'eip155:1/erc20:******************************************'),
('B', 'POLS', 'eip155:1/erc20:******************************************'),
('B', 'JUNO', 'JUNO'),
('B', 'NODL', 'NODL'),
('B', 'BSX', 'BSX'),
('B', 'APT', 'APT'),
('B', 'PARA', 'PARA'),
('B', 'STX', 'STX'),
('B', 'EUL', 'eip155:1/erc20:******************************************'),
('B', 'FLR', 'FLR'),
('B', 'WAXL', 'eip155:1/erc20:******************************************'),
('B', 'HFT', 'eip155:1/erc20:******************************************'),
('B', 'TBTC', 'eip155:1/erc20:******************************************'),
('B', 'BLUR', 'eip155:1/erc20:******************************************'),
('B', 'HDX', 'HDX'),
('B', 'EURT', 'eip155:1/erc20:******************************************'),
('B', 'GMX', 'eip155:42161/erc20:******************************************'),
('B', 'SUI', 'SUI'),
('B', 'LMWR', 'eip155:1/erc20:******************************************'),
('B', 'PEPE', 'eip155:1/erc20:******************************************'),
('B', 'AVT', 'eip155:1/erc20:******************************************'),
('B', 'TUSD', 'eip155:1/erc20:******************************************'),
('B', 'OP', 'eip155:10/erc20:******************************************'),
('B', 'TIA', 'TIA'),
('B', 'SEI', 'SEI'),
('B', 'BRICK', 'eip155:42170/erc20:******************************************'),
('B', 'MOON', 'eip155:42170/erc20:******************************************'),
('B', 'JTO', 'JTO'),
('B', 'PYTH', 'PYTH'),
('B', 'POL', 'eip155:1/erc20:0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6'),
('B', 'BONK', 'eip155:1/erc20:0x1151CB3d861920e07a38e03eEAd12C32178567F6'),
('B', 'GENS', 'eip155:56/erc20:0x2CD14cbA3F26254beeD1d78158cd2B6F91809600'),
('B', 'JUP', 'JUP'),
('B', 'DYM', 'DYM'),
('B', 'PICA', 'PICA'),
('B', 'WIF', 'WIF'),
('B', 'OTP', 'eip155:1/erc20:0xaA7a9CA87d3694B5755f213B5D04094b8d0F0A6F'),
('W', 'GALAX', 'eip155:1/erc20:0x15D4c048F83bd7e37d49eA4C83a07267Ec4203dA'),
('W', 'BCHSV', 'BSV'),
('W', 'VRA', 'eip155:1/erc20:0xF411903cbC70a74d22900a5DE66A2dda66507255'),
('W', 'KEY', 'eip155:1/erc20:******************************************'),
('W', 'MTC', 'eip155:1/erc20:0x905E337c6c8645263D3521205Aa37bf4d034e745'),
('W', 'R', 'eip155:1/erc20:0x2ef52Ed7De8c5ce03a4eF0efbe9B7450F2D7Edc9'),
('W', 'FET', 'eip155:1/erc20:******************************************'),
('W', 'CAPP', 'eip155:1/erc20:0x11613b1f840bb5A40F8866d857e24DA126B79D73'),
('W', 'BLOC', 'eip155:1/erc20:0x6F919D67967a97EA36195A2346d9244E60FE0dDB'),
('W', 'WIN', 'WIN-3'),
('W', 'STX', 'STX-2'),
('W', 'CPC', 'eip155:1/erc20:0xfAE4Ee59CDd86e3Be9e8b90b53AA866327D7c090'),
('W', 'ONE', 'ONE-2'),
('W', 'YFDAI', 'eip155:1/erc20:0xf4CD3d3Fda8d7Fd6C5a500203e38640A70Bf9577'),
('W', 'ASD', 'eip155:1/erc20:******************************************'),
('W', 'MODEFI', 'eip155:1/erc20:******************************************'),
('W', 'KICK', 'eip155:1/erc20:0x824a50dF33AC1B41Afc52f4194E2e8356C17C3aC'),
('W', 'STC', 'eip155:1/erc20:0x15B543e986b8c34074DFc9901136d9355a537e7E'),
('W', 'SLP', 'eip155:1/erc20:******************************************'),
('W', 'PNT', 'eip155:1/erc20:******************************************'),
('W', 'CFG', 'eip155:1/erc20:0xc221b7E65FfC80DE234bbB6667aBDd46593D34F0'),
('W', 'TRB', 'eip155:1/erc20:0x88df592f8eb5d7bd38bfef7deb0fbc02cf3778a0'),
('W', 'GTC', 'eip155:1/erc20:******************************************'),
('W', 'FTT', 'eip155:1/erc20:******************************************'),
('W', 'EDG', 'EDG-2'),
('W', 'DATA', 'eip155:1/erc20:0x8f693ca8D21b157107184d29D398A8D082b38b76'),
('W', 'SUTER', 'eip155:1/erc20:0xAA2ce7Ae64066175E0B90497CE7d9c190c315DB4'),
('W', 'RMRK', 'RMRK'),
('W', 'ANT', 'eip155:1/erc20:******************************************'),
('W', 'ADX', 'eip155:1/erc20:******************************************'),
('W', 'IMX', 'eip155:1/erc20:******************************************'),
('W', 'ADS', 'eip155:1/erc20:0x3106a0a076BeDAE847652F42ef07FD58589E001f'),
('W', 'CTC', 'eip155:1/erc20:******************************************'),
('W', 'ALEPH', 'eip155:1/erc20:0x27702a26126e0B3702af63Ee09aC4d1A084EF628'),
('W', 'EPK', 'eip155:1/erc20:0xDaF88906aC1DE12bA2b1D2f7bfC94E9638Ac40c4'),
('W', 'ARNM', 'ARN'),
('W', 'PSL', 'eip155:1/erc20:0xC775C0C30840Cb9F51e21061B054ebf1A00aCC29'),
('W', 'EVER', 'eip155:1/erc20:0x29d578CEc46B50Fa5C88a99C6A4B70184C062953'),
('W', 'PERP', 'eip155:1/erc20:******************************************'),
('W', 'ALPHA', 'eip155:1/erc20:******************************************'),
('W', 'BADGER', 'eip155:1/erc20:******************************************'),
('W', 'INJ', 'eip155:1/erc20:******************************************'),
('W', 'MASK', 'eip155:1/erc20:******************************************'),
('W', 'C98', 'eip155:1/erc20:******************************************'),
('W', 'BIFI', 'eip155:56/erc20:******************************************'),
('W', 'TUSD', 'eip155:1/erc20:******************************************'),
('W', 'TIME', 'eip155:1/erc20:0x6531f133e6DeeBe7F2dcE5A0441aA7ef330B4e53'),
('W', 'CELR', 'eip155:1/erc20:******************************************'),
('W', 'QUICK', 'eip155:1/erc20:******************************************'),
('W', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('W', 'SUSD', 'eip155:1/erc20:******************************************'),
('W', 'CREAM', 'eip155:1/erc20:0x2ba592F78dB6436527729929AAf6c908497cB200'),
('W', 'TORN', 'eip155:1/erc20:******************************************'),
('W', 'FRAX', 'eip155:1/erc20:******************************************'),
('W', 'GHST', 'eip155:1/erc20:******************************************'),
('W', 'FXS', 'eip155:1/erc20:******************************************'),
('W', 'SDL', 'eip155:1/erc20:0xf1Dc500FdE233A4055e25e5BbF516372BC4F6871'),
('W', 'REQ', 'eip155:1/erc20:******************************************'),
('W', 'ZIL', 'eip155:1/erc20:******************************************'),
('W', 'ANKR', 'eip155:1/erc20:******************************************'),
('W', 'CHR', 'eip155:1/erc20:0x915044526758533dfB918ecEb6e44bc21632060D'),
('W', 'ARPA', 'eip155:1/erc20:******************************************'),
('W', 'SXP', 'eip155:1/erc20:******************************************'),
('W', 'VIDT', 'eip155:1/erc20:0x445f51299Ef3307dBD75036dd896565F5B4BF7A5'),
('W', 'AMPL', 'eip155:1/erc20:0xD46bA6D942050d489DBd938a2C909A5d5039A161'),
('W', 'DIA', 'eip155:1/erc20:******************************************'),
('W', 'RFUEL', 'eip155:1/erc20:0xaf9f549774ecEDbD0966C52f250aCc548D3F36E5'),
('W', 'REVV', 'eip155:1/erc20:******************************************'),
('W', 'FRONT', 'eip155:1/erc20:******************************************'),
('W', 'MIR', 'eip155:1/erc20:******************************************'),
('W', 'RNDR', 'eip155:1/erc20:******************************************'),
('W', 'DYP', 'eip155:1/erc20:******************************************'),
('W', 'DODO', 'eip155:1/erc20:******************************************'),
('W', 'PRQ', 'eip155:1/erc20:******************************************'),
('W', 'PYR', 'eip155:1/erc20:******************************************'),
('W', 'XCAD', 'eip155:1/erc20:0x7659CE147D0e714454073a5dd7003544234b6Aa0'),
('W', 'PROM', 'eip155:1/erc20:******************************************'),
('W', 'ELON', 'eip155:1/erc20:******************************************'),
('W', 'POLS', 'eip155:1/erc20:******************************************'),
('W', 'GMEE', 'eip155:1/erc20:0xD9016A907Dc0ECfA3ca425ab20B6b785B42F2373'),
('W', 'AIOZ', 'eip155:1/erc20:******************************************'),
('W', 'CLV', 'eip155:1/erc20:******************************************'),
('W', 'WRX', 'eip155:56/erc20:0x8e17ed70334C87eCE574C9d537BC153d8609e2a3'),
('W', 'ATA', 'eip155:1/erc20:0xA2120b9e674d3fC3875f415A7DF52e382F141225'),
('W', 'OM', 'eip155:1/erc20:0x2baEcDf43734F22FD5c152DB08E3C27233F0c7d2'),
('W', 'TRADE', 'eip155:1/erc20:0x6F87D756DAf0503d08Eb8993686c7Fc01Dc44fB1'),
('W', 'LINA', 'eip155:1/erc20:******************************************'),
('W', 'ONSTON', 'eip155:1/erc20:******************************************'),
('W', 'METIS', 'eip155:1/erc20:******************************************'),
('W', 'STG', 'eip155:1/erc20:******************************************'),
('W', 'CEEK', 'eip155:1/erc20:******************************************'),
('W', 'BETA', 'eip155:1/erc20:0xBe1a001FE942f96Eea22bA08783140B9Dcc09D28'),
('W', 'PSTAKE', 'eip155:1/erc20:0xfB5c6815cA3AC72Ce9F5006869AE67f18bF77006'),
('W', 'ACH', 'eip155:1/erc20:******************************************'),
('W', 'BUSD', 'eip155:1/erc20:******************************************'),
('W', 'MC', 'eip155:1/erc20:******************************************'),
('W', 'IOTX', 'eip155:1/erc20:******************************************'),
('W', 'HYVE', 'eip155:1/erc20:0xd794DD1CAda4cf79C9EebaAb8327a1B0507ef7d4'),
('W', 'GHX', 'eip155:1/erc20:0x728f30fa2f100742C7949D1961804FA8E0B1387d'),
('W', 'DPI', 'eip155:1/erc20:0x1494CA1F11D487c2bBe4543E90080AeBa4BA3C2b'),
('W', 'PHNX', 'eip155:1/erc20:0x38A2fDc11f526Ddd5a607C1F251C065f40fBF2f7'),
('W', 'SPI', 'eip155:1/erc20:0x9B02dD390a603Add5c07f9fd9175b7DABE8D63B7'),
('W', 'MSWAP', 'eip155:1/erc20:0xC005204856ee7035a13D8D7CdBbdc13027AFff90'),
('W', 'FRM', 'eip155:1/erc20:0xE5CAeF4Af8780E59Df925470b050Fb23C43CA68C'),
('W', 'SHR', 'eip155:1/erc20:0xd98F75b1A3261dab9eEd4956c93F33749027a964'),
('W', 'TOKO', 'eip155:1/erc20:0x0c963A1B52Eb97C5e457c7D76696F8b95c3087eD'),
('W', 'NOIA', 'eip155:1/erc20:0xa8c8CfB141A3bB59FEA1E2ea6B79b5ECBCD7b6ca'),
('W', 'RFOX', 'eip155:1/erc20:0xa1d6Df714F91DeBF4e0802A542E13067f31b8262'),
('W', 'MTV', 'eip155:1/erc20:0x6226e00bCAc68b0Fe55583B90A1d727C14fAB77f'),
('W', 'UBXT', 'eip155:1/erc20:0x8564653879a18C560E7C0Ea0E084c516C62F5653'),
('W', 'ORAI', 'eip155:1/erc20:0x4c11249814f11b9346808179Cf06e71ac328c1b5'),
('W', 'ZEE', 'eip155:1/erc20:0x2eDf094dB69d6Dcd487f1B3dB9febE2eeC0dd4c5'),
('W', 'LAYER', 'eip155:1/erc20:0x0fF6ffcFDa92c53F615a4A75D982f399C989366b'),
('W', 'DSLA', 'eip155:1/erc20:0x3aFfCCa64c2A6f4e3B6Bd9c64CD2C969EFd1ECBe'),
('W', '2CRZ', 'eip155:1/erc20:0x2C9C19cE3b15ae77C6d80aEc3C1194Cfd6F7F3fA'),
('W', 'DFYN', 'eip155:1/erc20:0x9695e0114e12C0d3A3636fAb5A18e6b737529023'),
('W', 'BURP', 'eip155:1/erc20:0x33f391F4c4fE802b70B77AE37670037A92114A7c'),
('W', 'LPOOL', 'eip155:1/erc20:0x6149C26Cd2f7b5CCdb32029aF817123F6E37Df5B'),
('W', 'HAPI', 'eip155:1/erc20:0xD9c2D319Cd7e6177336b0a9c93c21cb48d84Fb54'),
('W', 'FORM', 'eip155:1/erc20:0x21381e026Ad6d8266244f2A583b35F9E4413FA2a'),
('W', 'ODDZ', 'eip155:1/erc20:0xCd2828fc4D8E8a0eDe91bB38CF64B1a81De65Bf6'),
('W', 'XPR', 'eip155:1/erc20:0xD7EFB00d12C2c13131FD319336Fdf952525dA2af'),
('W', 'WOO', 'eip155:1/erc20:******************************************'),
('W', 'SDAO', 'eip155:1/erc20:******************************************'),
('W', 'DEXE', 'eip155:1/erc20:0xde4EE8057785A7e8e800Db58F9784845A5C2Cbd6'),
('W', 'YLD', 'eip155:1/erc20:0xF94b5C5651c888d928439aB6514B93944eEE6F48'),
('W', 'TEL', 'eip155:1/erc20:0x467Bccd9d29f223BcE8043b84E8C8B282827790F'),
('W', 'PLATO', 'eip155:137/erc20:0x79637D860380bd28dF5a07329749654790FAc1Df'),
('W', 'VEMP', 'eip155:1/erc20:0xcFEB09C3c5F0f78aD72166D55f9e6E9A60e96eEC'),
('W', 'BONDLY', 'eip155:1/erc20:0xD2dDa223b2617cB616c1580db421e4cFAe6a8a85'),
('W', 'URUS', 'eip155:1/erc20:0xc6DdDB5bc6E61e0841C54f3e723Ae1f3A807260b'),
('W', 'POLC', 'eip155:1/erc20:0xaA8330FB2B4D5D07ABFE7A72262752a8505C6B37'),
('W', 'XTM', 'eip155:1/erc20:0xCd1fAFf6e578Fa5cAC469d2418C95671bA1a62Fe'),
('W', 'TIDAL', 'eip155:1/erc20:0x29CbD0510EEc0327992CD6006e63F9Fa8E7f33B7'),
('W', 'CTSI', 'eip155:1/erc20:******************************************'),
('W', 'ROOBEE', 'eip155:1/erc20:0xA31B1767e09f842ECFd4bc471Fe44F830E3891AA'),
('W', 'KAI', 'eip155:1/erc20:0xD9Ec3ff1f8be459Bb9369b4E79e9Ebcf7141C093'),
('W', 'OPCT', 'eip155:1/erc20:0xDb05EA0877A2622883941b939f0bb11d1ac7c400'),
('W', 'CTI', 'eip155:1/erc20:0x8c18D6a985Ef69744b9d57248a45c0861874f244'),
('W', 'CAS', 'eip155:1/erc20:0xe8780B48bdb05F928697A5e8155f672ED91462F7'),
('W', 'ALPA', 'eip155:1/erc20:0x7cA4408137eb639570F8E647d9bD7B7E8717514A'),
('W', 'MITX', 'eip155:1/erc20:0x4a527d8fc13C5203AB24BA0944F4Cb14658D1Db6'),
('W', 'ORBS', 'eip155:1/erc20:******************************************'),
('W', 'XCUR', 'eip155:1/erc20:0xE1c7E30C42C24582888C758984f6e382096786bd'),
('W', 'LABS', 'eip155:1/erc20:0x8b0E42F366bA502d787BB134478aDfAE966C8798'),
('W', 'EQZ', 'eip155:1/erc20:0x1Da87b114f35E1DC91F72bF57fc07A768Ad40Bb0'),
('W', 'HORD', 'eip155:1/erc20:******************************************'),
('W', 'CGG', 'eip155:1/erc20:******************************************'),
('W', 'TOWER', 'eip155:1/erc20:******************************************'),
('W', 'FLY', 'eip155:1/erc20:******************************************'),
('W', 'GOVI', 'eip155:1/erc20:******************************************'),
('W', 'MAHA', 'eip155:1/erc20:******************************************'),
('W', 'GLCH', 'eip155:1/erc20:******************************************'),
('W', 'ETHO', 'eip155:56/erc20:******************************************'),
('W', 'MARSH', 'eip155:1/erc20:******************************************'),
('W', 'ROUTE', 'eip155:1/erc20:******************************************'),
('W', 'NORD', 'eip155:1/erc20:******************************************'),
('W', 'EPIK', 'eip155:1/erc20:******************************************'),
('W', 'PBR', 'eip155:1/erc20:******************************************'),
('W', 'SWASH', 'eip155:1/erc20:******************************************'),
('W', 'MARS4', 'eip155:1/erc20:******************************************'),
('W', 'COV', 'eip155:1/erc20:******************************************'),
('W', 'CXO', 'eip155:1/erc20:******************************************'),
('W', 'MATTER', 'eip155:1/erc20:******************************************'),
('W', 'REEF', 'eip155:1/erc20:******************************************'),
('W', 'LTO', 'eip155:1/erc20:******************************************'),
('W', 'SUPER', 'eip155:1/erc20:******************************************'),
('W', 'XED', 'eip155:1/erc20:0xee573a945B01B788B9287CE062A0CFC15bE9fd86'),
('W', 'DPR', 'eip155:1/erc20:0xf3AE5d769e153Ef72b4e3591aC004E89F48107a1'),
('W', 'NUM', 'eip155:1/erc20:0x3496B523e5C00a4b4150D6721320CdDb234c3079'),
('W', 'UNIC', 'eip155:1/erc20:0x94E0BAb2F6Ab1F19F4750E42d7349f2740513aD5'),
('W', 'OVR', 'eip155:1/erc20:0x21BfBDa47A0B4B5b1248c767Ee49F7caA9B23697'),
('W', 'MOOV', 'eip155:1/erc20:0x24EC2Ca132abf8F6f8a6E24A1B97943e31f256a7'),
('W', 'UNO', 'eip155:1/erc20:0x474021845C4643113458ea4414bdb7fB74A01A77'),
('W', 'IOI', 'eip155:1/erc20:0x8B3870Df408fF4D7C3A26DF852D41034eDa11d81'),
('W', 'PMON', 'eip155:1/erc20:0x1796ae0b0fa4862485106a0de9b654eFE301D0b2'),
('W', 'HAKA', 'eip155:1/erc20:0xD85AD783cc94bd04196a13DC042A3054a9B52210'),
('W', 'EQX', 'eip155:1/erc20:0xBd3de9a069648c84d27d74d701C9fa3253098B15'),
('W', 'OPUL', 'eip155:1/erc20:0x80D55c03180349Fff4a229102F62328220A96444'),
('W', 'TLM', 'eip155:1/erc20:0x888888848B652B3E3a0f34c96E00EEC0F3a23F72'),
('W', 'NGL', 'eip155:1/erc20:0x2653891204F463fb2a2F4f412564b19e955166aE'),
('W', 'TRVL', 'eip155:1/erc20:0xd47bDF574B4F76210ed503e0EFe81B58Aa061F3d'),
('W', 'UNB', 'eip155:1/erc20:0x8dB253a1943DdDf1AF9bcF8706ac9A0Ce939d922'),
('W', 'DOSE', 'eip155:1/erc20:******************************************'),
('W', 'WOMBAT', 'eip155:1/erc20:0x0C9c7712C83B3C70e7c5E11100D33D9401BdF9dd'),
('W', 'STEPWATCH', 'eip155:137/erc20:0xabEDe05598760E399ed7EB24900b30C51788f00F'),
('W', 'UMB', 'eip155:1/erc20:0x6fC13EACE26590B80cCCAB1ba5d51890577D83B2'),
('W', 'DG', 'eip155:1/erc20:0x4b520c812E8430659FC9f12f6d0c39026C83588D'),
('W', 'FSN', 'eip155:1/erc20:0xD0352a019e9AB9d757776F532377aAEbd36Fd541'),
('W', 'STORE', 'eip155:1/erc20:0x31ea0de8119307aA264Bb4b38727aAb4E36b074f'),
('W', 'PIKASTER2', 'MLS'),
('W', 'WILD', 'eip155:1/erc20:0x2a3bFF78B79A009976EeA096a51A948a3dC00e34'),
('W', 'P00LS', 'eip155:1/erc20:0x881Ba05de1E78f549cC63a8f6Cabb1d4AD32250D'),
('W', 'DERC', 'eip155:1/erc20:0x9fa69536d1cda4A04cFB50688294de75B505a9aE'),
('W', 'MAGIC', 'eip155:1/erc20:******************************************'),
('W', 'HFT', 'eip155:1/erc20:******************************************'),
('W', 'MM', 'eip155:56/erc20:0x9df90628D40c72F85137e8cEE09dde353a651266'),
('W', 'PRIMAL', 'eip155:1/erc20:0xDd13DEdeCEbDA566322195bc4451D672A148752C'),
('W', 'FLOKI', 'eip155:1/erc20:******************************************'),
('W', 'WAXL', 'eip155:1/erc20:******************************************'),
('W', 'REP', 'eip155:1/erc20:******************************************'),
('W', 'ALPACA', 'eip155:56/erc20:******************************************'),
('W', 'RSR', 'eip155:1/erc20:******************************************'),
('W', 'GNS', 'eip155:137/erc20:******************************************'),
('W', 'SYN', 'eip155:1/erc20:******************************************'),
('W', 'BLZ', 'eip155:1/erc20:0x5732046a883704404f284ce41ffadd5b007fd668'),
('W', 'MONG', 'eip155:1/erc20:******************************************'),
('W', 'DPX', 'eip155:1/erc20:0xEec2bE5c91ae7f8a338e1e5f3b5DE49d07AfdC81'),
('W', 'AIPAD', 'eip155:1/erc20:0xE55d97A97ae6A17706ee281486E98A84095d8AAf'),
('W', 'LADYS', 'eip155:1/erc20:******************************************'),
('W', 'LMWR', 'eip155:1/erc20:******************************************'),
('W', 'WLD', 'eip155:1/erc20:******************************************'),
('W', 'PENDLE', 'eip155:1/erc20:******************************************'),
('W', 'LYX', 'LUKSO'),
('W', 'TON', 'eip155:1/erc20:******************************************'),
('W', 'PEPE20', 'eip155:1/erc20:******************************************'),
('W', 'VOLT', 'eip155:1/erc20:0x7f792db54B0e580Cdc755178443f0430Cf799aCa'),
('W', 'PEPE', 'eip155:1/erc20:******************************************'),
('W', 'CYBER', 'eip155:56/erc20:******************************************'),
('W', 'VELO', 'eip155:56/erc20:******************************************'),
('W', 'QI', 'eip155:43114/erc20:******************************************'),
('W', 'TAO', 'TAO'),
('W', 'KPOL', 'POL'),
('W', 'ZETA', 'eip155:1/erc20:******************************************'),
('W', 'SHRAP', 'eip155:1/erc20:0x31e4efe290973ebE91b3a875a7994f650942D28F'),
('W', 'AURA', 'eip155:1/erc20:0xC0c293ce456fF0ED870ADd98a0828Dd4d2903DBF'),
('W', 'ERN', 'eip155:1/erc20:******************************************'),
('W', 'STRIKE', 'eip155:1/erc20:0x74232704659ef37c08995e386A2E26cc27a8d7B1'),
('W', 'ARX', 'eip155:1/erc20:0x7d8DafF6d70CEAd12c6f077048552Cf89130A2B1'),
('W', 'MUBI', 'eip155:1/erc20:******************************************'),
('W', 'VANRY', 'eip155:1/erc20:0x8de5b80a0c1b02fe4976851d030b36122dbb8624'),
('W', 'NTVRK', 'eip155:1/erc20:0x52498f8d9791736f1d6398fe95ba3bd868114d10'),
('W', 'APP', 'eip155:1/erc20:******************************************'),
('[', 'NEXONEXO', 'eip155:1/erc20:******************************************'),
('[', 'GBPX', 'GBP'),
('e', 'AKITA', 'eip155:1/erc20:******************************************'),
('e', 'ALPHA', 'eip155:1/erc20:******************************************'),
('e', 'ANT', 'eip155:1/erc20:******************************************'),
('e', 'BADGER', 'eip155:1/erc20:******************************************'),
('e', 'BETH', 'ETH2'),
('e', 'CEEK', 'eip155:1/erc20:******************************************'),
('e', 'CEL', 'eip155:1/erc20:******************************************'),
('e', 'CELR', 'eip155:1/erc20:******************************************'),
('e', 'CLV', 'eip155:1/erc20:******************************************'),
('e', 'CTC', 'eip155:1/erc20:******************************************'),
('e', 'DEP', 'eip155:1/erc20:******************************************'),
('e', 'DHT', 'eip155:1/erc20:******************************************'),
('e', 'DIA', 'eip155:1/erc20:******************************************'),
('e', 'DOSE', 'eip155:1/erc20:******************************************'),
('e', 'ELON', 'eip155:1/erc20:******************************************'),
('e', 'EURT', 'eip155:1/erc20:******************************************'),
('e', 'FLOKI', 'eip155:1/erc20:******************************************'),
('e', 'FRONT', 'eip155:1/erc20:******************************************'),
('e', 'FSN', 'eip155:1/erc20:0xD0352a019e9AB9d757776F532377aAEbd36Fd541'),
('e', 'GHST', 'eip155:1/erc20:******************************************'),
('e', 'IMX', 'eip155:1/erc20:******************************************'),
('e', 'MAGIC', 'eip155:1/erc20:******************************************'),
('e', 'MASK', 'eip155:1/erc20:******************************************'),
('e', 'METIS', 'eip155:1/erc20:******************************************'),
('e', 'MIR', 'eip155:1/erc20:******************************************'),
('e', 'OM', 'eip155:1/erc20:******************************************'),
('e', 'ONE', 'ONE-2'),
('e', 'ORBS', 'eip155:1/erc20:******************************************'),
('e', 'PERP', 'eip155:1/erc20:******************************************'),
('e', 'POLS', 'eip155:1/erc20:******************************************'),
('e', 'PRQ', 'eip155:1/erc20:******************************************'),
('e', 'PSTAKE', 'eip155:1/erc20:0xfB5c6815cA3AC72Ce9F5006869AE67f18bF77006'),
('e', 'REVV', 'eip155:1/erc20:******************************************'),
('e', 'RFUEL', 'eip155:1/erc20:0xaf9f549774ecEDbD0966C52f250aCc548D3F36E5'),
('e', 'SLP', 'eip155:1/erc20:******************************************'),
('e', 'TRADE', 'eip155:1/erc20:0x6F87D756DAf0503d08Eb8993686c7Fc01Dc44fB1'),
('e', 'TRB', 'eip155:1/erc20:******************************************'),
('e', 'TUSD', 'eip155:1/erc20:******************************************'),
('e', 'VRA', 'eip155:1/erc20:0xF411903cbC70a74d22900a5DE66A2dda66507255'),
('e', 'WIN', 'WIN-3'),
('e', 'WOO', 'eip155:1/erc20:******************************************'),
('e', 'XPR', 'eip155:1/erc20:0xD7EFB00d12C2c13131FD319336Fdf952525dA2af'),
('e', 'YOYO', 'eip155:1/erc20:******************************************'),
('e', 'ZIL', 'eip155:1/erc20:******************************************'),
('e', 'PICKLE', 'eip155:1/erc20:0x429881672B9AE42b8EbA0E26cD9C73711b891Ca5'),
('e', 'FODL', 'eip155:1/erc20:0x4C2e59D098DF7b6cBaE0848d66DE2f8A4889b9C3'),
('e', 'ORS', 'eip155:1/erc20:0xEB9A4B185816C354dB92DB09cC3B50bE60b901b6'),
('e', 'REP', 'eip155:1/erc20:******************************************'),
('e', 'RSR', 'eip155:1/erc20:******************************************'),
('e', 'ID', 'eip155:56/erc20:0x2dff88a56767223a5529ea5960da7a3f5f766406'),
('e', 'ACH', 'eip155:56/erc20:0xBc7d6B50616989655AfD682fb42743507003056D'),
('e', 'XAUT', 'eip155:1/erc20:0x4922a015c4407F87432B179bb209e125432E4a2A'),
('e', 'VELODROME', 'eip155:10/erc20:0x3c8b650257cfb5f272f799f5e2b4e65093a11a05'),
('e', 'FXS', 'eip155:1/erc20:******************************************'),
('e', 'WLD', 'eip155:1/erc20:******************************************'),
('e', 'TON', 'eip155:1/erc20:******************************************'),
('e', 'PEPE', 'eip155:1/erc20:******************************************'),
('e', 'VELO', 'eip155:56/erc20:******************************************'),
('e', 'FET', 'eip155:1/erc20:******************************************'),
('e', 'RNDR', 'eip155:1/erc20:******************************************'),
('e', 'AST', 'eip155:1/erc20:******************************************'),
('e', 'INJ', 'eip155:1/erc20:******************************************'),
('e', 'VELA', 'eip155:42161/erc20:0x088cd8f5eF3652623c22D48b1605DCfE860Cd704'),
('e', 'ERN', 'eip155:1/erc20:******************************************'),
('e', 'ZETA', 'eip155:1/erc20:******************************************'),
('C', 'ONEINCH', 'eip155:1/erc20:******************************************'),
('C', 'AIR', 'AIR-2'),
('C', 'DEC', 'eip155:1/erc20:0x30f271C9E86D2B7d00a6376Cd96A1cFBD5F0b9b3'),
('C', 'BCHABC', 'BCH'),
('C', 'BCHSV', 'BSV'),
('C', 'CAI', 'CAIX'),
('C', 'CCN', 'CCN-2'),
('C', '', 'CCN'),
('C', 'CUSDT', 'cUSDT'),
('C', 'FAC', 'FAIR'),
('C', 'KEY', 'KEY-3'),
('C', 'MZC', 'MAZA'),
('C', 'MYR', 'XMY'),
('C', 'NBT', 'USNBT'),
('C', 'STR', 'XLM'),
('C', 'WC', 'XWC'),
('C', 'FTT', 'eip155:1/erc20:******************************************'),
('C', 'TRB', 'eip155:1/erc20:******************************************'),
('C', 'WIN', 'WIN-3'),
('C', 'GTC', 'eip155:1/erc20:******************************************'),
('C', 'DEXT', 'eip155:1/erc20:******************************************'),
('C', 'SLP', 'eip155:1/erc20:******************************************'),
('C', 'FET', 'eip155:1/erc20:******************************************'),
('C', 'IMX', 'eip155:1/erc20:******************************************'),
('C', 'RARE', 'eip155:1/erc20:0xba5BDe662c17e2aDFF1075610382B9B691296350'),
('C', 'NCT', 'eip155:1/erc20:******************************************'),
('C', 'CTC', 'eip155:1/erc20:******************************************'),
('C', 'WLUNA', 'eip155:1/erc20:******************************************'),
('C', 'MATH', 'eip155:1/erc20:0x08d967bb0134F2d07f7cfb6E246680c53927DD30'),
('C', 'PERP', 'eip155:1/erc20:******************************************'),
('C', 'SXP', 'eip155:1/erc20:******************************************'),
('C', 'FXS', 'eip155:1/erc20:******************************************'),
('C', 'CREAM', 'eip155:1/erc20:0x2ba592F78dB6436527729929AAf6c908497cB200'),
('C', 'ALPHA', 'eip155:1/erc20:******************************************'),
('C', 'ALICE', 'eip155:1/erc20:0xAC51066d7bEC65Dc4589368da368b212745d63E8'),
('C', 'BADGER', 'eip155:1/erc20:******************************************'),
('C', 'C98', 'eip155:1/erc20:******************************************'),
('C', 'WRX', 'eip155:56/erc20:0x8e17ed70334C87eCE574C9d537BC153d8609e2a3'),
('C', 'AMPL', 'eip155:1/erc20:0xD46bA6D942050d489DBd938a2C909A5d5039A161'),
('C', 'BUSD', 'eip155:1/erc20:******************************************'),
('C', 'GHST', 'eip155:1/erc20:******************************************'),
('C', 'INJ', 'eip155:1/erc20:******************************************'),
('C', 'LUSD', 'eip155:1/erc20:******************************************'),
('C', 'QUICK', 'eip155:1/erc20:******************************************'),
('C', 'REQ', 'eip155:1/erc20:******************************************'),
('C', 'TUSD', 'eip155:1/erc20:******************************************'),
('C', 'WETH', 'eip155:1/erc20:******************************************'),
('C', 'MASK', 'eip155:1/erc20:******************************************'),
('C', 'TORN', 'eip155:1/erc20:******************************************'),
('C', 'METIS', 'eip155:1/erc20:******************************************'),
('C', 'GNO', 'eip155:1/erc20:******************************************'),
('C', 'HIGH', 'eip155:1/erc20:******************************************'),
('C', 'FRAX', 'eip155:1/erc20:******************************************'),
('C', 'BIFI', 'eip155:56/erc20:******************************************'),
('C', 'ACH', 'eip155:1/erc20:******************************************'),
('C', 'ADD', 'eip155:1/erc20:******************************************'),
('C', 'AKITA', 'eip155:1/erc20:******************************************'),
('C', 'BLZ', 'eip155:1/erc20:******************************************'),
('C', 'CEEK', 'eip155:1/erc20:******************************************'),
('C', 'CTSI', 'eip155:1/erc20:******************************************'),
('C', 'CHR', 'eip155:1/erc20:0x915044526758533dfB918ecEb6e44bc21632060D'),
('C', 'CLV', 'eip155:1/erc20:******************************************'),
('C', 'DHT', 'eip155:1/erc20:******************************************'),
('C', 'ELON', 'eip155:1/erc20:******************************************'),
('C', 'DIA', 'eip155:1/erc20:******************************************'),
('C', 'DYP', 'eip155:1/erc20:******************************************'),
('C', 'FARM', 'eip155:1/erc20:******************************************'),
('C', 'FRONT', 'eip155:1/erc20:******************************************'),
('C', 'TIME', 'eip155:1/erc20:0x6531f133e6DeeBe7F2dcE5A0441aA7ef330B4e53'),
('C', 'FLOKI', 'eip155:1/erc20:******************************************'),
('C', 'FREN', 'eip155:1/erc20:0x37941b3Fdb2bD332e667D452a58Be01bcacb923e'),
('C', 'FSW', 'eip155:1/erc20:0xfffffffFf15AbF397dA76f1dcc1A1604F45126DB'),
('C', 'GMEE', 'eip155:1/erc20:0xD9016A907Dc0ECfA3ca425ab20B6b785B42F2373'),
('C', 'HEX', 'eip155:1/erc20:0x2b591e99afE9f32eAA6214f7B7629768c40Eeb39'),
('C', 'IDIA', 'eip155:56/erc20:0x0b15Ddf19D47E6a86A56148fb4aFFFc6929BcB89'),
('C', 'MAGIC', 'eip155:1/erc20:******************************************'),
('C', 'MATTER', 'eip155:1/erc20:******************************************'),
('C', 'SWAP', 'eip155:1/erc20:0xCC4304A31d09258b0029eA7FE63d032f52e44EFe'),
('C', 'TLM', 'eip155:1/erc20:0x888888848B652B3E3a0f34c96E00EEC0F3a23F72'),
('C', 'TRADE', 'eip155:1/erc20:0x6F87D756DAf0503d08Eb8993686c7Fc01Dc44fB1'),
('C', 'UMB', 'eip155:1/erc20:0x6fC13EACE26590B80cCCAB1ba5d51890577D83B2'),
('C', 'VSP', 'eip155:1/erc20:0x1b40183EFB4Dd766f11bDa7A7c3AD8982e998421'),
('C', 'WOO', 'eip155:1/erc20:******************************************'),
('C', 'XCAD', 'eip155:1/erc20:0x7659CE147D0e714454073a5dd7003544234b6Aa0'),
('C', 'YFX', 'eip155:1/erc20:0xF55a93b613D172b86c2Ba3981a849DaE2aeCDE2f'),
('C', 'ZAP', 'eip155:1/erc20:0x6781a0F84c7E9e846DCb84A9a5bd49333067b104'),
('C', 'RFUEL', 'eip155:1/erc20:0xaf9f549774ecEDbD0966C52f250aCc548D3F36E5'),
('C', 'RNDR', 'eip155:1/erc20:******************************************'),
('C', 'STAKE', 'eip155:1/erc20:0x0Ae055097C6d159879521C384F1D2123D1f195e6'),
('C', 'STG', 'eip155:1/erc20:******************************************'),
('C', 'SUPER', 'eip155:1/erc20:******************************************'),
('C', 'REVV', 'eip155:1/erc20:******************************************'),
('C', 'MIM', 'eip155:1/erc20:0x99D8a9C45b2ecA8864373A26D1459e3Dff1e17F3'),
('C', 'MIR', 'eip155:1/erc20:******************************************'),
('C', 'MTA', 'eip155:1/erc20:0xa3BeD4E1c75D00fa6f4E5E6922DB7261B5E9AcD2'),
('C', 'O3', 'eip155:1/erc20:******************************************'),
('C', 'OM', 'eip155:1/erc20:0x2baEcDf43734F22FD5c152DB08E3C27233F0c7d2'),
('C', 'ONSTON', 'eip155:1/erc20:******************************************'),
('C', 'PERX', 'eip155:1/erc20:0x3C6ff50c9Ec362efa359317009428d52115fe643'),
('C', 'POLS', 'eip155:1/erc20:******************************************'),
('C', 'PSP', 'eip155:1/erc20:0xcAfE001067cDEF266AfB7Eb5A286dCFD277f3dE5'),
('C', 'PYR', 'eip155:1/erc20:******************************************'),
('C', 'REEF', 'eip155:1/erc20:******************************************'),
('C', 'PRQ', 'eip155:1/erc20:******************************************'),
('C', 'MCB', 'eip155:1/erc20:******************************************'),
('C', 'MC', 'eip155:1/erc20:******************************************'),
('C', 'COMBO', 'eip155:1/erc20:******************************************'),
('C', 'HFT', 'eip155:1/erc20:******************************************'),
('C', 'TRR', 'eip155:56/erc20:******************************************'),
('C', 'CEL', 'eip155:1/erc20:******************************************'),
('C', 'TBTC', 'eip155:1/erc20:******************************************'),
('C', 'VOLT', 'eip155:1/erc20:******************************************'),
('C', 'GNS1', 'eip155:137/erc20:******************************************'),
('C', 'ALPACA', 'eip155:56/erc20:******************************************'),
('C', 'BTRFLY', 'eip155:1/erc20:******************************************'),
('C', 'PENDLE', 'eip155:1/erc20:******************************************'),
('C', 'MONG', 'eip155:1/erc20:******************************************'),
('C', 'RSR', 'eip155:1/erc20:******************************************'),
('C', 'SDAO', 'eip155:1/erc20:******************************************'),
('C', 'LADYS', 'eip155:1/erc20:******************************************'),
('C', 'COREUM', 'CORE'),
('C', 'TOKAMAK', 'eip155:1/erc20:******************************************'),
('C', 'WLD', 'eip155:1/erc20:******************************************'),
('C', 'TON', 'eip155:1/erc20:******************************************'),
('C', 'PEPE20', 'eip155:1/erc20:******************************************'),
('C', 'LOOT', 'eip155:1/erc20:******************************************'),
('C', 'SHIB2', 'eip155:1/erc20:******************************************'),
('C', 'BTC2', 'eip155:1/erc20:******************************************'),
('C', 'DOGE2', 'eip155:1/erc20:******************************************'),
('C', 'PMAN', 'eip155:1/erc20:******************************************'),
('C', 'ROCK', 'eip155:56/erc20:******************************************'),
('C', 'FRENNATION', 'eip155:56/erc20:******************************************'),
('C', 'HAM', 'eip155:1/erc20:******************************************'),
('C', 'BTM2', 'eip155:1/erc20:******************************************'),
('C', 'PERL', 'eip155:1/erc20:******************************************'),
('C', 'VELO', 'eip155:56/erc20:******************************************'),
('C', 'ERN', 'eip155:1/erc20:******************************************'),
('C', 'ALI', 'eip155:1/erc20:******************************************'),
('C', 'AERODROME', 'eip155:8453/erc20:******************************************'),
('C', 'LMWR', 'eip155:1/erc20:******************************************'),
('C', 'LUK', 'LUKSO'),
('C', 'MUBI', 'eip155:1/erc20:******************************************'),
('C', 'ZETA', 'eip155:1/erc20:******************************************'),
('C', 'TROVE', 'eip155:1/erc20:******************************************'),
('C', 'QI', 'eip155:43114/erc20:******************************************'),
('C', 'VEXT', 'eip155:43114/erc20:******************************************'),
('C', 'APP', 'eip155:1/erc20:******************************************'),
('a', 'BTC', 'BTC'),
('a', 'XRP', 'XRP'),
('a', 'ETH', 'ETH'),
('a', 'ADA', 'ADA'),
('a', 'ATOM', 'ATOM'),
('a', 'BCH', 'BCH'),
('a', 'BTG', 'BTG'),
('a', 'DASH', 'DASH'),
('a', 'DCR', 'DCR'),
('a', 'DGB', 'DGB'),
('a', 'DOGE', 'DOGE'),
('a', 'DOT', 'DOT'),
('a', 'ENJ', 'eip155:1/erc20:******************************************'),
('a', 'EOS', 'EOS'),
('a', 'FIL', 'FIL'),
('a', 'FLOW', 'FLOW'),
('a', 'HBAR', 'HBAR'),
('a', 'HNT', 'HNT'),
('a', 'MIOTA', 'IOTA'),
('a', 'LTC', 'LTC'),
('a', 'MKR', 'eip155:1/erc20:******************************************'),
('a', 'NANO', 'NANO'),
('a', 'NEO', 'NEO'),
('a', 'OMG', 'eip155:1/erc20:******************************************'),
('a', 'OXT', 'eip155:1/erc20:******************************************'),
('a', 'REN', 'eip155:1/erc20:******************************************'),
('a', 'SRM', 'eip155:1/erc20:******************************************'),
('a', 'THETA', 'eip155:1/erc20:******************************************'),
('a', 'TRX', 'eip155:1/erc20:******************************************'),
('a', 'VET', 'VET'),
('a', 'UMA', 'eip155:1/erc20:0x04Fa0d235C4abf4BcF4787aF4CF447DE572eF828'),
('a', 'XCH', 'XCH'),
('a', 'NEM', 'XEM'),
('a', 'XLM', 'XLM'),
('a', 'XTZ', 'XTZ'),
('a', 'ZIL', 'eip155:1/erc20:******************************************'),
('a', 'ZRX', 'eip155:1/erc20:******************************************'),
('l', 'WOO', 'eip155:1/erc20:******************************************'),
('l', 'STG', 'eip155:1/erc20:******************************************'),
('l', 'ONE', 'ONE-2'),
('l', 'DODO', 'eip155:1/erc20:******************************************'),
('l', 'RNDR', 'eip155:1/erc20:******************************************'),
('l', 'FXS', 'eip155:1/erc20:******************************************'),
('l', 'IMX', 'eip155:1/erc20:******************************************'),
('l', 'RSR', 'eip155:1/erc20:******************************************'),
('l', 'PERP', 'eip155:1/erc20:******************************************'),
('l', 'WLD', 'eip155:1/erc20:******************************************'),
('l', 'LDO', 'eip155:1/erc20:******************************************'),
('l', 'MAGIC', 'eip155:1/erc20:******************************************'),
('l', 'WSM', 'eip155:1/erc20:******************************************'),
('l', 'FTM_WOO_2', 'eip155:250/erc20:******************************************'),
('l', 'AVAXC_USDC2', 'eip155:1/erc20:******************************************'),
('l', 'AVAXC', 'AVAX'),
('l', 'BCHSV', 'BSV'),
('l', 'BSC_BTCB', 'BTC'),
('l', 'ETH-AETH', 'ETH'),
('l', 'ETH_AXS_NEW', 'eip155:1/erc20:******************************************'),
('l', 'ETH_DAI_V1', 'eip155:1/erc20:******************************************'),
('l', 'ETH_LRCV2', 'eip155:1/erc20:******************************************'),
('l', 'ETH_UNISWAP', 'eip155:1/erc20:******************************************'),
('l', 'TRON', 'TRX'),
('l', 'UATOM', 'ATOM'),
('l', 'STFX', 'eip155:1/erc20:******************************************'),
('l', 'QI', 'eip155:43114/erc20:******************************************'),
('l', 'METIS', 'eip155:1/erc20:******************************************'),
('l', 'C98', 'eip155:1/erc20:******************************************'),
('l', 'INJ', 'eip155:1/erc20:******************************************'),
('l', 'PYR', 'eip155:1/erc20:******************************************'),
('l', 'ZETA', 'eip155:1/erc20:******************************************');