from typing import Final

from rotkehlchen.chain.evm.constants import ZERO_ADDRESS
from rotkehlchen.chain.evm.decoding.hop.structures import HopBridgeEventData
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.constants.assets import A_ETH

BRIDGES: Final = {
    string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_ETH.identifier,
        amm_wrapper=string_to_evm_address('******************************************'),
        hop_identifier='eip155:10/erc20:******************************************',
        saddle_swap=string_to_evm_address('******************************************'),
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier='eip155:10/erc20:******************************************',
        amm_wrapper=string_to_evm_address('******************************************'),
        hop_identifier='eip155:10/erc20:******************************************',
        saddle_swap=string_to_evm_address('******************************************'),
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier='eip155:10/erc20:******************************************',
        amm_wrapper=string_to_evm_address('******************************************'),
        hop_identifier='eip155:10/erc20:0x2057C8ECB70Afd7Bee667d76B4CD373A325b1a20',
        saddle_swap=string_to_evm_address('0xeC4B41Af04cF917b54AEb6Df58c0f8D78895b5Ef'),
    ), string_to_evm_address('0x7191061D5d4C60f598214cC6913502184BAddf18'): HopBridgeEventData(
        identifier='eip155:10/erc20:0xDA10009cBd5D07dd0CeCc66161FC93D7c9000da1',
        amm_wrapper=string_to_evm_address('0xb3C68a491608952Cb1257FC9909a537a0173b63B'),
        hop_identifier='eip155:10/erc20:0x56900d66D74Cb14E3c86895789901C9135c95b16',
        saddle_swap=string_to_evm_address('0xF181eD90D6CfaC84B8073FdEA6D34Aa744B41810'),
    ), string_to_evm_address('0x03D7f750777eC48d39D080b020D83Eb2CB4e3547'): HopBridgeEventData(
        identifier='eip155:10/erc20:0xc5102fE9359FD9a28f877a67E36B0F050d81a3CC',
        amm_wrapper=ZERO_ADDRESS,
        hop_identifier='eip155:10/erc20:0xc5102fE9359FD9a28f877a67E36B0F050d81a3CC',
    ), string_to_evm_address('0x16284c7323c35F4960540583998C98B1CfC581a7'): HopBridgeEventData(
        identifier='eip155:10/erc20:0x8700dAec35aF8Ff88c16BdF0418774CB3D7599B4',
        amm_wrapper=string_to_evm_address('0xf11EBB94EC986EA891Aec29cfF151345C83b33Ec'),
        hop_identifier='eip155:10/erc20:0x13B7F51BD865410c3AcC4d56083C5B56aB38D203',
        saddle_swap=string_to_evm_address('0x1990BC6dfe2ef605Bfc08f5A23564dB75642Ad73'),
    ), string_to_evm_address('0x33Fe5bB8DA466dA55a8A32D6ADE2BB104E2C5201'): HopBridgeEventData(
        identifier='eip155:10/erc20:0x8c6f28f2F1A3C87F0f938b96d27520d9751ec8d9',
        amm_wrapper=string_to_evm_address('0x29Fba7d2A6C95DB162ee09C6250e912D6893DCa6'),
        hop_identifier='eip155:10/erc20:0x6F03052743CD99ce1b29265E377e320CD24Eb632',
        saddle_swap=string_to_evm_address('0x8d4063E82A4Db8CdAed46932E1c71e03CA69Bede'),
    ), string_to_evm_address('0xA0075E8cE43dcB9970cB7709b9526c1232cc39c2'): HopBridgeEventData(
        identifier='eip155:10/erc20:0x9Bcef72be871e61ED4fBbc7630889beE758eb81D',
        amm_wrapper=string_to_evm_address('0x19B2162CA4C2C6F08C6942bFB846ce5C396aCB75'),
        hop_identifier='eip155:10/erc20:0x755569159598f3702bdD7DFF6233A317C156d3Dd',
        saddle_swap=string_to_evm_address('******************************************'),
    ),
}

REWARD_CONTRACTS: Final = {
    string_to_evm_address('******************************************'),  # HOP (USDC.e)
    string_to_evm_address('******************************************'),  # HOP (USDT)
    string_to_evm_address('******************************************'),  # HOP (DAI)
    string_to_evm_address('******************************************'),  # HOP (ETH)
    string_to_evm_address('******************************************'),  # HOP (SNX)
    string_to_evm_address('******************************************'),  # OP (SNX)
    string_to_evm_address('******************************************'),  # HOP (sUSD)
    string_to_evm_address('******************************************'),  # OP (sUSD)
    string_to_evm_address('******************************************'),  # RPL (rETH)
}
