from typing import Final

from rotkehlchen.chain.evm.decoding.hop.structures import HopBridgeEventData
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.constants.assets import A_DAI, A_ETH, A_ETH_MATIC, A_SNX, A_SUSD, A_USDC, A_USDT

HOP_GOVERNOR: Final = string_to_evm_address('******************************************')

BRIDGES: Final = {
    string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_ETH.identifier,
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_USDC.identifier,
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_USDT.identifier,
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_ETH_MATIC.identifier,
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_DAI.identifier,
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier='eip155:1/erc20:******************************************',
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_SNX.identifier,
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier=A_SUSD.identifier,
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier='eip155:1/erc20:******************************************',
    ), string_to_evm_address('******************************************'): HopBridgeEventData(
        identifier='eip155:1/erc20:******************************************',
    ),
}
