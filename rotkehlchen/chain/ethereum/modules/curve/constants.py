from typing import Final

from rot<PERSON><PERSON>chen.chain.evm.types import string_to_evm_address

GAUGE_BRIBE_V2: Final = string_to_evm_address('0x7893bbb46613d7a4FbcC31Dab4C9b823FfeE1026')
FEE_DISTRIBUTOR: Final = string_to_evm_address('0xA464e6DCda8AC41e03616F95f4BC98a13b8922Dc')
VOTING_ESCROW: Final = string_to_evm_address('0x5f3b5DfEb7B28CDbD7FAba78963EE202a494e2A2')
CLAIMED: Final = b'\x9c\xdc\xf2\xf7qL\xca5\x08\xc7\xf0\x11\x0b\x04\xa9\n\x80\xa3\xa8\xdd\x0e5\xde\x99h\x9d\xb7M(\xc58>'  # noqa: E501
VOTING_ESCROW_DEPOSIT: Final = b'Ef\xdf\xc2\x9fo\x11\xd1:A\x8c&\xa0+\xef|(\xba\xe7I\xd4\xdeG\xe4\xe6\xa7\xcd\xde\xa6s\rY'  # noqa: E501
VOTING_ESCROW_WITHDRAW: Final = b'\xf2y\xe6\xa1\xf5\xe3 \xcc\xa9\x115gm\x9c\xb6\xe4L\xa8\xa0\x8c\x0b\x884+\xcd\xb1\x14Oe\x11\xb5h'  # noqa: E501

GAUGE_BRIBE_V2_ASSETS: Final = [
    string_to_evm_address('0xD533a949740bb3306d119CC777fa900bA034cd52'),  # CRV
    string_to_evm_address('******************************************'),  # CVX
    string_to_evm_address('******************************************'),  # EURS
    string_to_evm_address('******************************************'),  # SPELL
]

# Deposit contracts are retrieved from the links below Deposit<pool>:
# https://curve.readthedocs.io/ref-addresses.html#base-pools
# https://curve.readthedocs.io/ref-addresses.html#metapools
#
# The duplicates were found via Etherscan:
# https://etherscan.io/find-similar-contracts?a=******************************************
CURVE_DEPOSIT_CONTRACTS: Final = {
    string_to_evm_address('******************************************'),  # curve usdn
    string_to_evm_address('******************************************'),  # curve usdn duplicate
    string_to_evm_address('******************************************'),  # curve ust
    string_to_evm_address('******************************************'),  # curve usdp
    string_to_evm_address('******************************************'),  # curve usdk
    string_to_evm_address('******************************************'),  # curve usdk duplicate
    string_to_evm_address('******************************************'),  # curve rsv
    string_to_evm_address('******************************************'),  # curve musd
    string_to_evm_address('******************************************'),  # curve musd duplicate
    string_to_evm_address('******************************************'),  # curve linkusd
    string_to_evm_address('0xF6bDc2619FFDA72c537Cd9605e0A274Dc48cB1C9'),  # curve linkusd duplicate  # noqa: E501
    string_to_evm_address('0x09672362833d8f703D5395ef3252D4Bfa51c15ca'),  # curve husd
    string_to_evm_address('0x0a53FaDa2d943057C47A301D25a4D9b3B8e01e8E'),  # curve husd duplicate
    string_to_evm_address('0x64448B78561690B70E17CBE8029a3e5c1bB7136e'),  # curve gusd
    string_to_evm_address('0x0aE274c98c0415C0651AF8cF52b010136E4a0082'),  # curve gusd duplicate
    string_to_evm_address('0x61E10659fe3aa93d036d099405224E4Ac24996d0'),  # curve dusd
    string_to_evm_address('0xbBC81d23Ea2c3ec7e56D39296F0cbB648873a5d3'),  # curve y
    string_to_evm_address('0xb6c057591E073249F2D9D88Ba59a46CFC9B59EdB'),  # curve busd
    string_to_evm_address('******************************************'),  # curve compound
    string_to_evm_address('******************************************'),  # curve pax
    string_to_evm_address('******************************************'),  # curve susd v2
    string_to_evm_address('******************************************'),  # curve zap
    string_to_evm_address('******************************************'),  # curve usdt
    string_to_evm_address('******************************************'),  # curve bbtc
    string_to_evm_address('******************************************'),  # curve obtc
    string_to_evm_address('******************************************'),  # curve pbtc
    string_to_evm_address('******************************************'),  # curve tbtc
    string_to_evm_address('******************************************'),  # curve 3pool
    string_to_evm_address('******************************************'),  # curve fraxusdc
    string_to_evm_address('******************************************'),  # curve sbtc
    string_to_evm_address('******************************************'),  # curve sbtc2
}
DEPOSIT_AND_STAKE_ZAP: Final = string_to_evm_address('******************************************')
GAUGE_CONTROLLER: Final = string_to_evm_address('******************************************')
CURVE_SWAP_ROUTER: Final = string_to_evm_address('******************************************')
AAVE_POOLS: Final = {
    string_to_evm_address('******************************************'),  # aDAI + aUSDC + aUSDT
    string_to_evm_address('******************************************'),  # aDAI + aSUSD
}
