from typing import TYPE_CHECKING

from rotkehlchen.chain.evm.decoding.hop.decoder import HopCommonDecoder

from .constants import BRIDGES, REWARD_CONTRACTS

if TYPE_CHECKING:
    from rotkehlchen.chain.base.node_inquirer import BaseInquirer
    from rotkehlchen.chain.evm.decoding.base import BaseDecoderTools
    from rotkehlchen.user_messages import MessagesAggregator


class HopDecoder(HopCommonDecoder):

    def __init__(
            self,
            base_inquirer: 'BaseInquirer',
            base_tools: 'BaseDecoderTools',
            msg_aggregator: 'MessagesAggregator',
    ) -> None:
        super().__init__(
            evm_inquirer=base_inquirer,
            base_tools=base_tools,
            msg_aggregator=msg_aggregator,
            bridges=BRIDGES,
            reward_contracts=REWARD_CONTRACTS,
        )
