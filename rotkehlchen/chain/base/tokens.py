from typing import TYPE_CHECKING

from rotkehlchen.chain.evm.tokens import EvmTokens

if TYPE_CHECKING:
    from rotkehlchen.db.d<PERSON><PERSON><PERSON> import <PERSON><PERSON><PERSON><PERSON>

    from .node_inquirer import BaseInquirer


class BaseTokens(EvmTokens):

    def __init__(self, database: '<PERSON><PERSON><PERSON>ler', base_inquirer: 'BaseInquirer') -> None:
        super().__init__(database=database, evm_inquirer=base_inquirer)
