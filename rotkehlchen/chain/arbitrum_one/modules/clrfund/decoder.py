import logging
from typing import TYPE_CHECKING

from rotkehlchen.assets.asset import EvmToken
from rotkehlchen.chain.evm.decoding.clrfund.decoder import ClrfundCommonDecoder
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.logging import Rot<PERSON><PERSON>chenLogsAdapter

if TYPE_CHECKING:
    from rotkehlchen.chain.evm.decoding.base import BaseDecoderTools
    from rotkehlchen.chain.evm.node_inquirer import EvmNodeInquirer
    from rotkehlchen.user_messages import MessagesAggregator

logger = logging.getLogger(__name__)
log = RotkehlchenLogsAdapter(logger)


class ClrfundDecoder(ClrfundCommonDecoder):

    def __init__(
            self,
            evm_inquirer: 'EvmNodeInquirer',
            base_tools: 'BaseDecoderTools',
            msg_aggregator: 'MessagesAggregator',  # pylint: disable=unused-argument
    ) -> None:
        super().__init__(
            evm_inquirer=evm_inquirer,
            base_tools=base_tools,
            msg_aggregator=msg_aggregator,
            rounds_data=[
                (string_to_evm_address('******************************************'), string_to_evm_address('******************************************'), 'Ethstaker round', EvmToken('eip155:42161/erc20:******************************************')),  # noqa: E501
                (string_to_evm_address('******************************************'), string_to_evm_address('******************************************'), 'ETHColombia round', EvmToken('eip155:42161/erc20:******************************************')),  # noqa: E501
                (string_to_evm_address('******************************************'), string_to_evm_address('******************************************'), 'Round 9', EvmToken('eip155:42161/erc20:******************************************')),  # noqa: E501
            ],
        )
