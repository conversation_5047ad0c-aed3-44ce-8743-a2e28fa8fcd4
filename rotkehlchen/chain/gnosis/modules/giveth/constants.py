from typing import Final

from rotkehlchen.chain.evm.types import string_to_evm_address

GIVPOW_ADDRESS: Final = string_to_evm_address('0xD93d3bDBa18ebcB3317a57119ea44ed2Cf41C2F2')
GNOSIS_GIVPOWERSTAKING_WRAPPER: Final = string_to_evm_address('0x24F2d06446AF8D6E89fEbC205e7936a602a87b60')  # noqa: E501
GIV_TOKEN_ID: Final = 'eip155:100/erc20:0x4f4F9b8D5B4d0Dc10506e5551B0513B61fD59e75'
GGIV_TOKEN_ID: Final = 'eip155:100/erc20:0xfFBAbEb49be77E5254333d5fdfF72920B989425f'
