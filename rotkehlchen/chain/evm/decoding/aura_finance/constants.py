from typing import Final

from eth_typing import ABI

from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.types import ChainID

CPT_AURA_FINANCE: Final = 'aura-finance'

CHAIN_ID_TO_BOOSTER_ADDRESSES: Final = {
    ChainID.BASE: string_to_evm_address('******************************************'),
    ChainID.GNOSIS: string_to_evm_address('******************************************'),
    ChainID.ETHEREUM: string_to_evm_address('******************************************'),
    ChainID.OPTIMISM: string_to_evm_address('******************************************'),
    ChainID.POLYGON_POS: string_to_evm_address('******************************************'),
    ChainID.ARBITRUM_ONE: string_to_evm_address('******************************************'),
}

AURA_BOOSTER_ABI: ABI = [
  {
    'inputs': [
      {
        'name': '',
        'type': 'uint256',
      },
    ],
    'name': 'poolInfo',
    'outputs': [
      {
        'name': 'lptoken',
        'type': 'address',
      },
      {
        'name': 'token',
        'type': 'address',
      },
      {
        'name': 'gauge',
        'type': 'address',
      },
      {
        'name': 'crvRewards',
        'type': 'address',
      },
      {
        'name': 'stash',
        'type': 'address',
      },
      {
        'name': 'shutdown',
        'type': 'bool',
      },
    ],
    'stateMutability': 'view',
    'type': 'function',
  },
    {
    'inputs': [],
    'name': 'poolLength',
    'outputs': [
      {
        'name': '',
        'type': 'uint256',
      },
    ],
    'stateMutability': 'view',
    'type': 'function',
  },
]
