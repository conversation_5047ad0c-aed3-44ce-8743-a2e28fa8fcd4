from typing import Final

CPT_AAVE: Final = 'aave'
CPT_AAVE_V1: Final = 'aave-v1'
CPT_AAVE_V2: Final = 'aave-v2'
CPT_AAVE_V3: Final = 'aave-v3'

LIQUIDATION_CALL: Final = b'\xe4\x13\xa3!\xe8h\x1d\x83\x1fM\xbc\xcb\xcay\r)R\xb5o\x97y\x08\xe4[\xe3s5S>\x00R\x86'  # noqa: E501
ENABLE_COLLATERAL: Final = b'\x00\x05\x8aV\xea\x94e<\xdfO\x15-"z\xce"\xd4\xc0\n\xd9\x9e*C\xf5\x8c\xb7\xd9\xe3\xfe\xb2\x95\xf2'  # noqa: E501
DISABLE_COLLATERAL: Final = b'D\xc5\x8d\x816[f\xddK\x1a\x7f6\xc2Z\xa9{\x8cq\xc3a\xeeI7\xad\xc1\xa0\x00\x00"}\xb5\xdd'  # noqa: E501
WITHDRAW: Final = b'1\x15\xd1D\x9a{s,\x98l\xba\x18$N\x89zE\x0fa\xe1\xbb\x8dX\x9c\xd2\xe6\x9el\x89$\xf9\xf7'  # noqa: E501
MINT: Final = b'E\x8f_\xa4\x12\xd0\xf6\x9b\x08\xdd\x84\x87+\x02\x15g\\\xc6{\xc1\xd5\xb6\xfd\x930\n\x1c8x\xb8a\x96'  # noqa: E501
