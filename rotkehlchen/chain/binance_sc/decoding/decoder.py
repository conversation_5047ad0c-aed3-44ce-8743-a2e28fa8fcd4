from typing import TYPE_CHECKING

from rotkehlchen.chain.evm.decoding.base import BaseDecoderTools
from rotkehlchen.chain.evm.decoding.decoder import EVMTransactionDecoder
from rotkehlchen.constants.assets import A_BSC_BNB
from rotkehlchen.types import ChecksumEvmAddress

if TYPE_CHECKING:
    from rotkehlchen.chain.binance_sc.node_inquirer import BinanceSCInquirer
    from rotkehlchen.chain.binance_sc.transactions import BinanceSCTransactions
    from rotkehlchen.db.dbhandler import DB<PERSON>andler


class BinanceSCTransactionDecoder(EVMTransactionDecoder):

    def __init__(
            self,
            database: '<PERSON><PERSON>and<PERSON>',
            binance_sc_inquirer: 'BinanceSCInquirer',
            transactions: 'BinanceSCTransactions',
    ):
        super().__init__(
            database=database,
            evm_inquirer=binance_sc_inquirer,
            transactions=transactions,
            value_asset=A_BSC_BNB.resolve_to_asset_with_oracles(),
            event_rules=[],
            misc_counterparties=[],
            base_tools=BaseDecoderTools(
                database=database,
                evm_inquirer=binance_sc_inquirer,
                is_non_conformant_erc721_fn=self._is_non_conformant_erc721,
                address_is_exchange_fn=self._address_is_exchange,
            ),
        )

    # -- methods that need to be implemented by child classes --

    @staticmethod
    def _is_non_conformant_erc721(address: ChecksumEvmAddress) -> bool:  # pylint: disable=unused-argument
        return False

    @staticmethod
    def _address_is_exchange(address: ChecksumEvmAddress) -> str | None:  # pylint: disable=unused-argument
        return None
