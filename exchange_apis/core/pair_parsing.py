"""
Comprehensive pair parsing for different exchanges
"""
import re
from typing import Tuple, Dict, List, Optional, Set
from dataclasses import dataclass

from .types import Asset, Location, UnprocessableTradePair
from .asset_converters import asset_from_kraken, asset_from_binance, asset_from_coinbase


@dataclass
class TradingPair:
    """Represents a trading pair with base and quote assets"""
    symbol: str
    base_asset: Asset
    quote_asset: Asset
    location: Location


class KrakenPairParser:
    """Kraken pair parsing logic"""

    def __init__(self):
        # Common quote assets in Kraken, ordered by preference for parsing
        self.quote_assets = [
            'USD', 'EUR', 'GBP', 'CAD', 'JPY', 'CHF', 'AUD', 'KRW',  # Fiat
            'ZUSD', 'ZEUR', 'ZGBP', 'ZCAD', 'ZJPY', 'ZCHF', 'ZAUD', 'ZKRW',  # Kraken fiat
            'BTC', 'ETH', 'USDT', 'USDC', 'DAI',  # Crypto
            'XXBT', 'XETH',  # Kraken crypto
        ]

        # Known Kraken pairs cache (would be populated from API in real implementation)
        self.known_pairs: Dict[str, Tuple[str, str]] = {}

    def parse_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """
        Parse Kraken trading pair into base and quote assets

        Kraken pairs can be complex:
        - XXBTZUSD (XBT/USD)
        - XETHZEUR (ETH/EUR)
        - ADAUSD (ADA/USD)
        - DOTUSD (DOT/USD)
        - etc.
        """
        if not pair:
            raise UnprocessableTradePair(pair)

        # Check if we have this pair cached
        if pair in self.known_pairs:
            base_str, quote_str = self.known_pairs[pair]
            return asset_from_kraken(base_str, keep_kraken_format=True), asset_from_kraken(quote_str, keep_kraken_format=True)

        # Special handling for complex Kraken pairs
        base_str, quote_str = self._parse_kraken_pair_advanced(pair)

        try:
            base_asset = asset_from_kraken(base_str, keep_kraken_format=True)
            quote_asset = asset_from_kraken(quote_str, keep_kraken_format=True)
            # Cache the result
            self.known_pairs[pair] = (base_str, quote_str)
            return base_asset, quote_asset
        except Exception:
            raise UnprocessableTradePair(f"Cannot parse Kraken pair: {pair}")

    def _parse_kraken_pair_advanced(self, pair: str) -> Tuple[str, str]:
        """Advanced Kraken pair parsing with special case handling"""

        # Handle special dot notation (e.g., ETH2.SETH)
        if '.' in pair:
            parts = pair.split('.')
            if len(parts) == 2:
                return parts[0], parts[1]

        # Try to parse using quote asset patterns first
        for quote in self.quote_assets:
            if pair.endswith(quote):
                base_str = pair[:-len(quote)]
                if base_str:  # Make sure we have a base asset
                    return base_str, quote

        # Special patterns for Kraken's complex naming
        # Pattern: XXBTZUSD -> XXBT + ZUSD
        if len(pair) == 8:
            # Try 4-4 split first
            base_str = pair[:4]
            quote_str = pair[4:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

            # Try other splits for 8-character pairs
            for split_pos in [3, 5]:
                base_str = pair[:split_pos]
                quote_str = pair[split_pos:]
                if self._is_valid_kraken_asset_pair(base_str, quote_str):
                    return base_str, quote_str

        # Pattern: XETHZEUR -> XETH + ZEUR (7 chars)
        elif len(pair) == 7:
            # Try 4-3 split first (common for XETH + EUR)
            base_str = pair[:4]
            quote_str = pair[4:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

            # Try 3-4 split
            base_str = pair[:3]
            quote_str = pair[3:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

        # Standard 6-character pairs
        elif len(pair) == 6:
            # Try 3-3 split
            base_str = pair[:3]
            quote_str = pair[3:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

        # For other lengths, try different splits
        else:
            for split_pos in range(2, len(pair) - 1):
                base_str = pair[:split_pos]
                quote_str = pair[split_pos:]
                if self._is_valid_kraken_asset_pair(base_str, quote_str):
                    return base_str, quote_str

        # Fallback: assume last 3-4 characters are quote
        for quote_len in [4, 3]:
            if len(pair) > quote_len:
                base_str = pair[:-quote_len]
                quote_str = pair[-quote_len:]
                if base_str and quote_str:
                    return base_str, quote_str

        # Final fallback
        if len(pair) >= 4:
            return pair[:-3], pair[-3:]
        else:
            return pair, ""

    def _is_valid_kraken_asset_pair(self, base: str, quote: str) -> bool:
        """Check if base/quote combination looks like valid Kraken assets"""
        if not base or not quote:
            return False

        # Check if quote is a known quote asset
        if quote in self.quote_assets:
            return True

        # Check if both are known Kraken assets
        from .asset_converters import KRAKEN_ASSET_MAP
        if base in KRAKEN_ASSET_MAP and quote in KRAKEN_ASSET_MAP:
            return True

        # Check patterns - Kraken assets often start with X or Z
        if quote.startswith(('Z', 'X')) and len(quote) >= 3:
            return True

        # Common crypto quote patterns
        if quote in ['BTC', 'ETH', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF']:
            return True

        return False


class BinancePairParser:
    """Binance pair parsing logic"""

    def __init__(self):
        # Common quote assets in Binance, ordered by preference
        self.quote_assets = [
            'USDT', 'BUSD', 'USDC', 'TUSD', 'PAX', 'USDP',  # Stablecoins
            'BTC', 'ETH', 'BNB',  # Major crypto
            'USD', 'EUR', 'GBP', 'AUD', 'TRY', 'RUB', 'UAH',  # Fiat
            'BIDR', 'BKRW', 'IDRT', 'NGN', 'PLN', 'RON', 'ZAR',  # Other fiat
        ]

        # Cache for known pairs
        self.known_pairs: Dict[str, Tuple[str, str]] = {}

    def parse_symbol(self, symbol: str) -> Tuple[Asset, Asset]:
        """
        Parse Binance symbol into base and quote assets

        Binance symbols are generally straightforward:
        - BTCUSDT (BTC/USDT)
        - ETHBTC (ETH/BTC)
        - ADAEUR (ADA/EUR)
        """
        if not symbol:
            raise UnprocessableTradePair(symbol)

        # Check cache first
        if symbol in self.known_pairs:
            base_str, quote_str = self.known_pairs[symbol]
            return asset_from_binance(base_str), asset_from_binance(quote_str)

        # Try to match against known quote assets
        for quote in self.quote_assets:
            if symbol.endswith(quote):
                base_str = symbol[:-len(quote)]
                if base_str:  # Make sure we have a base asset
                    try:
                        base_asset = asset_from_binance(base_str)
                        quote_asset = asset_from_binance(quote)
                        # Cache the result
                        self.known_pairs[symbol] = (base_str, quote)
                        return base_asset, quote_asset
                    except Exception:
                        continue

        # Fallback: assume last 3-4 characters are quote
        for quote_len in [4, 3]:
            if len(symbol) > quote_len:
                base_str = symbol[:-quote_len]
                quote_str = symbol[-quote_len:]
                try:
                    base_asset = asset_from_binance(base_str)
                    quote_asset = asset_from_binance(quote_str)
                    self.known_pairs[symbol] = (base_str, quote_str)
                    return base_asset, quote_asset
                except Exception:
                    continue

        raise UnprocessableTradePair(f"Cannot parse Binance symbol: {symbol}")


class CoinbasePairParser:
    """Coinbase pair parsing logic"""

    def parse_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """
        Parse Coinbase pair

        Coinbase pairs are usually separated by '-':
        - BTC-USD
        - ETH-EUR
        - ADA-BTC
        """
        if not pair:
            raise UnprocessableTradePair(pair)

        if '-' in pair:
            parts = pair.split('-')
            if len(parts) == 2:
                base_str, quote_str = parts
                return asset_from_coinbase(base_str), asset_from_coinbase(quote_str)

        raise UnprocessableTradePair(f"Cannot parse Coinbase pair: {pair}")


class KuCoinPairParser:
    """KuCoin pair parsing logic"""

    def parse_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """
        Parse KuCoin pair

        KuCoin pairs are usually separated by '-':
        - BTC-USDT
        - ETH-BTC
        - ADA-USD
        """
        if not pair:
            raise UnprocessableTradePair(pair)

        if '-' in pair:
            parts = pair.split('-')
            if len(parts) == 2:
                base_str, quote_str = parts
                return asset_from_kucoin(base_str), asset_from_kucoin(quote_str)

        raise UnprocessableTradePair(f"Cannot parse KuCoin pair: {pair}")


class UniversalPairParser:
    """Universal pair parser that handles all exchanges"""

    def __init__(self):
        self.kraken_parser = KrakenPairParser()
        self.binance_parser = BinancePairParser()
        self.coinbase_parser = CoinbasePairParser()
        self.kucoin_parser = KuCoinPairParser()

    def parse_pair(self, pair: str, location: Location) -> Tuple[Asset, Asset]:
        """Parse trading pair based on exchange location"""
        if location == Location.KRAKEN:
            return self.kraken_parser.parse_pair(pair)
        elif location in (Location.BINANCE, Location.BINANCEUS):
            return self.binance_parser.parse_symbol(pair)
        elif location in (Location.COINBASE, Location.COINBASEPRO):
            return self.coinbase_parser.parse_pair(pair)
        elif location == Location.KUCOIN:
            return self.kucoin_parser.parse_pair(pair)
        else:
            # Generic fallback
            return self._generic_parse(pair)

    def _generic_parse(self, pair: str) -> Tuple[Asset, Asset]:
        """Generic pair parsing for unknown exchanges"""
        # Try common separators
        for separator in ['-', '_', '/']:
            if separator in pair:
                parts = pair.split(separator)
                if len(parts) == 2:
                    return Asset(parts[0]), Asset(parts[1])

        # Try common quote assets
        common_quotes = ['USDT', 'USD', 'BTC', 'ETH', 'EUR']
        for quote in common_quotes:
            if pair.endswith(quote):
                base = pair[:-len(quote)]
                if base:
                    return Asset(base), Asset(quote)

        raise UnprocessableTradePair(f"Cannot parse pair: {pair}")
