"""
Core types for exchange APIs - minimal dependencies version
"""
from enum import IntEnum
from typing import NewType, Union
from decimal import Decimal

# Basic type aliases
Timestamp = NewType('Timestamp', int)
AssetAmount = NewType('AssetAmount', Decimal)
Price = NewType('Price', Decimal)
Fee = NewType('Fee', Decimal)
ApiKey = NewType('ApiKey', str)
ApiSecret = NewType('ApiSecret', bytes)


class TradeType(IntEnum):
    """Trade type enumeration"""
    BUY = 1
    SELL = 2
    SETTLEMENT_BUY = 3
    SETTLEMENT_SELL = 4

    def __str__(self) -> str:
        return self.name.lower()

    @classmethod
    def from_string(cls, value: str) -> 'TradeType':
        """Convert string to TradeType"""
        value = value.strip().lower()
        if value in {'buy', 'limit_buy'}:
            return cls.BUY
        elif value in {'sell', 'limit_sell'}:
            return cls.SELL
        elif value in {'settlement_buy', 'settlement buy'}:
            return cls.SETTLEMENT_BUY
        elif value in {'settlement_sell', 'settlement sell'}:
            return cls.SETTLEMENT_SELL
        else:
            raise ValueError(f"Unknown trade type: {value}")


class Location(IntEnum):
    """Exchange location enumeration"""
    EXTERNAL = 1
    KRAKEN = 2
    POLONIEX = 3
    BITTREX = 4
    BINANCE = 5
    BITMEX = 6
    COINBASE = 7
    COINBASEPRO = 11
    GEMINI = 12
    CRYPTOCOM = 16
    BITSTAMP = 18
    BINANCEUS = 19
    BITFINEX = 20
    BITCOINDE = 21
    ICONOMI = 22
    KUCOIN = 23
    FTX = 26
    NEXO = 27
    BLOCKFI = 28
    INDEPENDENTRESERVE = 29
    BITPANDA = 34
    BISQ = 35
    FTXUS = 36
    OKX = 37
    HTX = 41
    BYBIT = 42
    WOO = 43
    COINBASEPRIME = 44

    def __str__(self) -> str:
        return self.name.lower()

    @classmethod
    def from_string(cls, value: str) -> 'Location':
        """Convert string to Location"""
        try:
            return cls[value.upper()]
        except KeyError:
            raise ValueError(f"Unknown location: {value}")


class Asset:
    """Simple asset representation"""

    def __init__(self, identifier: str, symbol: str = None, name: str = None):
        self.identifier = identifier
        self.symbol = symbol or identifier
        self.name = name or identifier

    def __str__(self) -> str:
        return self.symbol

    def __repr__(self) -> str:
        return f"Asset('{self.identifier}')"

    def __eq__(self, other) -> bool:
        if isinstance(other, Asset):
            return self.identifier == other.identifier
        return False

    def __hash__(self) -> int:
        return hash(self.identifier)


# Common assets
A_BTC = Asset('BTC', 'BTC', 'Bitcoin')
A_ETH = Asset('ETH', 'ETH', 'Ethereum')
A_USD = Asset('USD', 'USD', 'US Dollar')
A_EUR = Asset('EUR', 'EUR', 'Euro')
A_USDT = Asset('USDT', 'USDT', 'Tether')
A_USDC = Asset('USDC', 'USDC', 'USD Coin')


def create_asset(identifier: str, symbol: str = None, name: str = None) -> Asset:
    """Factory function to create assets"""
    return Asset(identifier, symbol, name)


# Exception classes for asset handling
class UnknownAsset(Exception):
    """Exception raised when an unknown asset is encountered"""
    def __init__(self, identifier: str):
        self.identifier = identifier
        super().__init__(f"Unknown asset: {identifier}")


class UnsupportedAsset(Exception):
    """Exception raised when an unsupported asset is encountered"""
    def __init__(self, identifier: str):
        self.identifier = identifier
        super().__init__(f"Unsupported asset: {identifier}")


class UnprocessableTradePair(Exception):
    """Exception raised when a trade pair cannot be processed"""
    def __init__(self, pair: str):
        self.pair = pair
        super().__init__(f"Cannot process trade pair: {pair}")
