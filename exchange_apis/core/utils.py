"""
Utility functions for exchange APIs
"""
import time
import json
import hashlib
import hmac
import base64
import requests
from typing import Dict, Any, Optional, Union, List
from urllib.parse import urlencode
import logging

from .types import <PERSON><PERSON><PERSON><PERSON>, ApiSecret, Timestamp

logger = logging.getLogger(__name__)


class RateLimiter:
    """Simple rate limiter"""

    def __init__(self, calls_per_second: float = 1.0):
        self.calls_per_second = calls_per_second
        self.last_call = 0.0

    def wait_if_needed(self):
        """Wait if necessary to respect rate limit"""
        now = time.time()
        time_since_last = now - self.last_call
        min_interval = 1.0 / self.calls_per_second

        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            time.sleep(sleep_time)

        self.last_call = time.time()


class APIError(Exception):
    """Base exception for API errors"""
    pass


class RateLimitError(APIError):
    """Rate limit exceeded"""
    pass


class AuthenticationError(APIError):
    """Authentication failed"""
    pass


def make_request(
    url: str,
    method: str = 'GET',
    params: Optional[Dict[str, Any]] = None,
    data: Optional[Union[Dict[str, Any], str]] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    retries: int = 3,
) -> Dict[str, Any]:
    """
    Make HTTP request with error handling and retries
    """
    session = requests.Session()
    if headers:
        session.headers.update(headers)

    for attempt in range(retries + 1):
        try:
            if method.upper() == 'GET':
                response = session.get(url, params=params, timeout=timeout)
            elif method.upper() == 'POST':
                # Handle both dict and string data
                if isinstance(data, str):
                    response = session.post(url, params=params, data=data, timeout=timeout)
                else:
                    response = session.post(url, params=params, json=data, timeout=timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            if response.status_code == 429:
                if attempt < retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                    time.sleep(wait_time)
                    continue
                else:
                    raise RateLimitError("Rate limit exceeded")

            if response.status_code == 401:
                raise AuthenticationError("Authentication failed")

            if response.status_code >= 400:
                raise APIError(f"HTTP {response.status_code}: {response.text}")

            return response.json()

        except requests.exceptions.RequestException as e:
            if attempt < retries:
                wait_time = 2 ** attempt
                logger.warning(f"Request failed, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
                continue
            else:
                raise APIError(f"Request failed after {retries} retries: {e}")

    raise APIError("Unexpected error in make_request")


def create_signature_hmac_sha256(secret: ApiSecret, message: str) -> str:
    """Create HMAC-SHA256 signature"""
    return hmac.new(secret, message.encode(), hashlib.sha256).hexdigest()


def create_signature_hmac_sha512(secret: ApiSecret, message: str) -> str:
    """Create HMAC-SHA512 signature"""
    return hmac.new(secret, message.encode(), hashlib.sha512).hexdigest()


def create_signature_base64(secret: ApiSecret, message: str) -> str:
    """Create base64 encoded HMAC-SHA256 signature"""
    signature = hmac.new(secret, message.encode(), hashlib.sha256).digest()
    return base64.b64encode(signature).decode()


def timestamp_to_ms(timestamp: Union[int, float]) -> int:
    """Convert timestamp to milliseconds"""
    return int(timestamp * 1000)


def timestamp_from_ms(timestamp_ms: int) -> Timestamp:
    """Convert milliseconds timestamp to seconds"""
    return Timestamp(int(timestamp_ms / 1000))


def current_timestamp() -> Timestamp:
    """Get current timestamp"""
    return Timestamp(int(time.time()))


def parse_decimal_safe(value: Union[str, int, float]) -> float:
    """Safely parse decimal value"""
    try:
        return float(str(value))
    except (ValueError, TypeError):
        return 0.0
