#!/usr/bin/env python3
"""
Test script for Kraken pair parsing validation

This script tests the Kraken pair parser against the provided CSV files:
- kraken_allpairs_test.csv: Contains Kraken trading pairs
- kraken_allpairs_parsed.csv: Contains expected parsing results

Usage:
    python test_kraken_pairs.py
"""

import csv
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import exchange_apis
sys.path.insert(0, str(Path(__file__).parent.parent))

from exchange_apis.core.pair_parsing import KrakenPairParser
from exchange_apis.core.asset_converters import asset_from_kraken
from exchange_apis.core.types import UnprocessableTradePair


def load_test_pairs(filename: str) -> list[str]:
    """Load trading pairs from CSV file"""
    pairs = []
    filepath = Path(__file__).parent / filename
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if row and row[0].strip():  # Skip empty rows
                    pairs.append(row[0].strip())
    except FileNotFoundError:
        print(f"Error: Could not find {filename}")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading {filename}: {e}")
        sys.exit(1)
    
    return pairs


def load_expected_results(filename: str) -> dict[str, str]:
    """Load expected parsing results from CSV file"""
    expected = {}
    filepath = Path(__file__).parent / filename
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if len(row) >= 2 and row[0].strip() and row[1].strip():
                    pair = row[0].strip()
                    expected_result = row[1].strip()
                    expected[pair] = expected_result
    except FileNotFoundError:
        print(f"Error: Could not find {filename}")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading {filename}: {e}")
        sys.exit(1)
    
    return expected


def format_parsed_result(base_asset, quote_asset) -> str:
    """Format the parsed result to match expected format"""
    return f"{base_asset.identifier}_{quote_asset.identifier}"


def test_kraken_pair_parsing():
    """Main test function"""
    print("=" * 70)
    print("Kraken Pair Parsing Validation Test")
    print("=" * 70)
    
    # Load test data
    print("Loading test data...")
    test_pairs = load_test_pairs('kraken_allpairs_test.csv')
    expected_results = load_expected_results('kraken_allpairs_parsed.csv')
    
    print(f"Loaded {len(test_pairs)} test pairs")
    print(f"Loaded {len(expected_results)} expected results")
    
    if len(test_pairs) != len(expected_results):
        print(f"Warning: Mismatch in number of test pairs ({len(test_pairs)}) and expected results ({len(expected_results)})")
    
    # Initialize parser
    parser = KrakenPairParser()
    
    # Test results
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    parsing_errors = 0
    
    print("\nRunning tests...")
    print("-" * 70)
    
    # Track different types of failures
    failures = {
        'parsing_failed': [],
        'result_mismatch': [],
        'missing_expected': []
    }
    
    for pair in test_pairs:
        total_tests += 1
        
        # Check if we have expected result for this pair
        if pair not in expected_results:
            failures['missing_expected'].append(pair)
            failed_tests += 1
            continue
        
        expected = expected_results[pair]
        
        try:
            # Parse the pair
            base_asset, quote_asset = parser.parse_pair(pair)
            actual = format_parsed_result(base_asset, quote_asset)
            
            # Compare with expected result
            if actual == expected:
                passed_tests += 1
                print(f"✅ {pair:15} -> {actual:20} (PASS)")
            else:
                failed_tests += 1
                failures['result_mismatch'].append((pair, expected, actual))
                print(f"❌ {pair:15} -> {actual:20} (FAIL - expected: {expected})")
                
        except UnprocessableTradePair as e:
            parsing_errors += 1
            failed_tests += 1
            failures['parsing_failed'].append((pair, str(e)))
            print(f"💥 {pair:15} -> PARSING FAILED: {e}")
            
        except Exception as e:
            parsing_errors += 1
            failed_tests += 1
            failures['parsing_failed'].append((pair, str(e)))
            print(f"💥 {pair:15} -> ERROR: {e}")
    
    # Print summary
    print("\n" + "=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    print(f"Total tests:      {total_tests}")
    print(f"Passed:           {passed_tests}")
    print(f"Failed:           {failed_tests}")
    print(f"Parsing errors:   {parsing_errors}")
    print(f"Success rate:     {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed failure analysis
    if failed_tests > 0:
        print("\n" + "-" * 70)
        print("FAILURE ANALYSIS")
        print("-" * 70)
        
        if failures['parsing_failed']:
            print(f"\nParsing failures ({len(failures['parsing_failed'])}):")
            for pair, error in failures['parsing_failed'][:10]:  # Show first 10
                print(f"  {pair}: {error}")
            if len(failures['parsing_failed']) > 10:
                print(f"  ... and {len(failures['parsing_failed']) - 10} more")
        
        if failures['result_mismatch']:
            print(f"\nResult mismatches ({len(failures['result_mismatch'])}):")
            for pair, expected, actual in failures['result_mismatch'][:10]:  # Show first 10
                print(f"  {pair}: expected '{expected}', got '{actual}'")
            if len(failures['result_mismatch']) > 10:
                print(f"  ... and {len(failures['result_mismatch']) - 10} more")
        
        if failures['missing_expected']:
            print(f"\nMissing expected results ({len(failures['missing_expected'])}):")
            for pair in failures['missing_expected'][:10]:  # Show first 10
                print(f"  {pair}")
            if len(failures['missing_expected']) > 10:
                print(f"  ... and {len(failures['missing_expected']) - 10} more")
    
    # Asset conversion test
    print("\n" + "-" * 70)
    print("ASSET CONVERSION TEST")
    print("-" * 70)
    
    # Test some common Kraken asset conversions
    test_assets = [
        ('XXBT', 'BTC'),
        ('XETH', 'ETH'),
        ('ZUSD', 'USD'),
        ('ZEUR', 'EUR'),
        ('XBT', 'BTC'),
        ('SETH', 'ETH2'),
        ('XDG', 'DOGE'),
        ('ADA', 'ADA'),
        ('DOT', 'DOT'),
    ]
    
    asset_tests_passed = 0
    for kraken_name, expected_name in test_assets:
        try:
            asset = asset_from_kraken(kraken_name)
            if asset.identifier == expected_name:
                asset_tests_passed += 1
                print(f"✅ {kraken_name:8} -> {asset.identifier:8} (PASS)")
            else:
                print(f"❌ {kraken_name:8} -> {asset.identifier:8} (FAIL - expected: {expected_name})")
        except Exception as e:
            print(f"💥 {kraken_name:8} -> ERROR: {e}")
    
    print(f"\nAsset conversion: {asset_tests_passed}/{len(test_assets)} passed")
    
    # Final result
    print("\n" + "=" * 70)
    if failed_tests == 0:
        print("🎉 ALL TESTS PASSED! 🎉")
        return True
    else:
        print(f"❌ {failed_tests} TESTS FAILED")
        return False


if __name__ == "__main__":
    success = test_kraken_pair_parsing()
    sys.exit(0 if success else 1)
