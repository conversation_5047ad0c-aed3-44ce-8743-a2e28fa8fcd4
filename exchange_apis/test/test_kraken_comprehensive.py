#!/usr/bin/env python3
"""
Comprehensive Kraken pair parsing test using official API data

This script:
1. Loads Assets and AssetPairs API responses from Kraken
2. Uses this data to improve pair parsing accuracy
3. Tests parsing on all known Kraken pairs
4. Generates a CSV file with all parsed results for manual verification

Usage:
    python test_kraken_comprehensive.py
"""

import json
import csv
import sys
from pathlib import Path
from typing import Dict, Any, Tu<PERSON>, List

# Add the parent directories to the path so we can import exchange_apis
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent))

from exchange_apis.core.asset_converters import asset_from_kraken
from exchange_apis.core.types import Asset


class KrakenAPIDataLoader:
    """Loads and processes Kraken API data"""
    
    def __init__(self):
        self.assets: Dict[str, Dict[str, Any]] = {}
        self.asset_pairs: Dict[str, Dict[str, Any]] = {}
        self.asset_name_map: Dict[str, str] = {}  # Maps Kraken names to standard names
        
    def load_api_data(self):
        """Load both Assets and AssetPairs API responses"""
        base_path = Path(__file__).parent
        
        # Load Assets API data
        assets_file = base_path / "Assets_api_data.json"
        try:
            with open(assets_file, 'r', encoding='utf-8') as f:
                assets_data = json.load(f)
                self.assets = assets_data.get('result', {})
                print(f"Loaded {len(self.assets)} assets from Kraken API")
        except Exception as e:
            print(f"Error loading assets data: {e}")
            return False
        
        # Load AssetPairs API data
        pairs_file = base_path / "AssetPairs_api_response.json"
        try:
            with open(pairs_file, 'r', encoding='utf-8') as f:
                pairs_data = json.load(f)
                self.asset_pairs = pairs_data.get('result', {})
                print(f"Loaded {len(self.asset_pairs)} asset pairs from Kraken API")
        except Exception as e:
            print(f"Error loading asset pairs data: {e}")
            return False
        
        # Build asset name mapping
        self._build_asset_name_map()
        return True
    
    def _build_asset_name_map(self):
        """Build mapping from Kraken asset names to standard names"""
        for kraken_name, asset_info in self.assets.items():
            altname = asset_info.get('altname', kraken_name)
            
            # Use asset converter to get standard name
            try:
                standard_asset = asset_from_kraken(kraken_name)
                self.asset_name_map[kraken_name] = standard_asset.identifier
                
                # Also map altname if different
                if altname != kraken_name:
                    standard_asset_alt = asset_from_kraken(altname)
                    self.asset_name_map[altname] = standard_asset_alt.identifier
                    
            except Exception as e:
                # Fallback to using as-is
                self.asset_name_map[kraken_name] = kraken_name
                if altname != kraken_name:
                    self.asset_name_map[altname] = altname
        
        print(f"Built asset name mapping for {len(self.asset_name_map)} assets")
    
    def get_standard_asset_name(self, kraken_name: str) -> str:
        """Get standard asset name from Kraken asset name"""
        return self.asset_name_map.get(kraken_name, kraken_name)


class EnhancedKrakenPairParser:
    """Enhanced Kraken pair parser using official API data"""
    
    def __init__(self, api_loader: KrakenAPIDataLoader):
        self.api_loader = api_loader
        
    def parse_pair_with_api_data(self, pair_name: str) -> Tuple[str, str]:
        """
        Parse Kraken pair using official API data
        
        Returns tuple of (base_asset_standard_name, quote_asset_standard_name)
        """
        # First try to find in AssetPairs API data
        if pair_name in self.api_loader.asset_pairs:
            pair_info = self.api_loader.asset_pairs[pair_name]
            base_kraken = pair_info.get('base', '')
            quote_kraken = pair_info.get('quote', '')
            
            if base_kraken and quote_kraken:
                base_standard = self.api_loader.get_standard_asset_name(base_kraken)
                quote_standard = self.api_loader.get_standard_asset_name(quote_kraken)
                return base_standard, quote_standard
        
        # Fallback: try to parse manually (this shouldn't happen with complete API data)
        return self._fallback_parse(pair_name)
    
    def _fallback_parse(self, pair_name: str) -> Tuple[str, str]:
        """Fallback parsing when API data is not available"""
        # This is a simplified fallback - in practice, all pairs should be in API data
        return pair_name, "UNKNOWN"


def test_all_kraken_pairs():
    """Test parsing of all known Kraken pairs"""
    print("=" * 80)
    print("Comprehensive Kraken Pair Parsing Test")
    print("=" * 80)
    
    # Load API data
    api_loader = KrakenAPIDataLoader()
    if not api_loader.load_api_data():
        print("Failed to load API data")
        return False
    
    # Initialize enhanced parser
    parser = EnhancedKrakenPairParser(api_loader)
    
    # Get all known pairs from API data
    all_pairs = list(api_loader.asset_pairs.keys())
    print(f"\nTesting {len(all_pairs)} pairs from Kraken AssetPairs API")
    
    # Test results
    results = []
    successful_parses = 0
    failed_parses = 0
    
    print("\nParsing pairs...")
    for i, pair_name in enumerate(all_pairs):
        try:
            base_standard, quote_standard = parser.parse_pair_with_api_data(pair_name)
            
            # Format result
            result_format = f"{base_standard}_{quote_standard}"
            results.append({
                'pair': pair_name,
                'base': base_standard,
                'quote': quote_standard,
                'formatted': result_format,
                'status': 'SUCCESS'
            })
            successful_parses += 1
            
            # Show progress every 50 pairs
            if (i + 1) % 50 == 0:
                print(f"  Processed {i + 1}/{len(all_pairs)} pairs...")
                
        except Exception as e:
            results.append({
                'pair': pair_name,
                'base': 'ERROR',
                'quote': 'ERROR',
                'formatted': f'ERROR: {str(e)}',
                'status': 'FAILED'
            })
            failed_parses += 1
    
    # Print summary
    print(f"\n" + "=" * 80)
    print("PARSING SUMMARY")
    print("=" * 80)
    print(f"Total pairs:        {len(all_pairs)}")
    print(f"Successful parses:  {successful_parses}")
    print(f"Failed parses:      {failed_parses}")
    print(f"Success rate:       {(successful_parses/len(all_pairs)*100):.1f}%")
    
    # Save results to CSV
    output_file = Path(__file__).parent / "kraken_all_pairs_parsed.csv"
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['pair', 'parsed_result'])  # Header
            
            for result in results:
                writer.writerow([result['pair'], result['formatted']])
        
        print(f"\nResults saved to: {output_file}")
        print(f"Format: pair,base_quote")
        
    except Exception as e:
        print(f"Error saving results: {e}")
    
    # Show some examples
    print(f"\n" + "-" * 80)
    print("SAMPLE RESULTS (first 20)")
    print("-" * 80)
    for result in results[:20]:
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
        print(f"{status_icon} {result['pair']:15} -> {result['formatted']}")
    
    if len(results) > 20:
        print(f"... and {len(results) - 20} more pairs")
    
    # Show asset mapping examples
    print(f"\n" + "-" * 80)
    print("ASSET MAPPING EXAMPLES")
    print("-" * 80)
    sample_assets = ['XXBT', 'XETH', 'ZUSD', 'ZEUR', 'ZGBP', 'ADA', 'DOT', 'LINK']
    for asset in sample_assets:
        if asset in api_loader.asset_name_map:
            standard = api_loader.asset_name_map[asset]
            print(f"  {asset:8} -> {standard}")
    
    return successful_parses == len(all_pairs)


if __name__ == "__main__":
    success = test_all_kraken_pairs()
    
    print(f"\n" + "=" * 80)
    if success:
        print("🎉 ALL PAIRS PARSED SUCCESSFULLY! 🎉")
    else:
        print("⚠️  Some pairs failed to parse - check the output above")
    
    print("\nNext steps:")
    print("1. Check 'kraken_all_pairs_parsed.csv' for complete results")
    print("2. Manually verify a sample of the parsed pairs")
    print("3. The CSV format is: pair,base_quote")
    print("=" * 80)
    
    sys.exit(0 if success else 1)
