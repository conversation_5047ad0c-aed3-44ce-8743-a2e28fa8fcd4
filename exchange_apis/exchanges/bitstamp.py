"""
Bitstamp exchange implementation
"""
import time
import hashlib
import hmac
import uuid
from typing import List, Dict, Any, Optional, Tu<PERSON>
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class BitstampExchange(BaseExchange):
    """Bitstamp exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.BITSTAMP,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://www.bitstamp.net/api",
        )
        
        # Bitstamp specific settings
        self.api_version = "v2"
        
    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate Bitstamp API credentials"""
        try:
            response = self._make_authenticated_request('balance')
            if 'error' in response:
                return False, f"API Error: {response['error']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Bitstamp"""
        trades = []
        
        try:
            # Bitstamp uses user_transactions endpoint
            limit = 1000  # Bitstamp max limit
            offset = 0
            
            while True:
                options = {
                    'limit': limit,
                    'offset': offset,
                    'sort': 'asc',
                }
                
                response = self._make_authenticated_request('user_transactions', params=options)
                
                if not isinstance(response, list):
                    break
                
                batch_trades = []
                for transaction in response:
                    # Only process trade transactions (type 2)
                    if transaction.get('type') == '2':
                        trade = self._parse_trade_data(transaction)
                        if trade and start_timestamp <= trade.timestamp <= end_timestamp:
                            batch_trades.append(trade)
                
                trades.extend(batch_trades)
                
                # Check if we got fewer results than requested (end of data)
                if len(response) < limit:
                    break
                
                offset += limit
            
        except Exception as e:
            print(f"Error querying Bitstamp trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Bitstamp authentication headers"""
        if not self.api_secret:
            return {}
        
        # Bitstamp uses unique nonce and timestamp
        nonce = str(uuid.uuid4())
        timestamp = str(int(time.time() * 1000))
        
        # Prepare payload
        payload_string = ''
        content_type = ''
        if params:
            payload_string = urlencode(params)
            content_type = 'application/x-www-form-urlencoded'
        
        # Create the message for signature
        api_path = f"/api/{self.api_version}/{endpoint.lstrip('/')}/"
        request_url = f"{self.base_url}{api_path}"
        query_params = ''
        
        message = (
            'BITSTAMP '
            f'{self.api_key}'
            f'{method.upper()}'
            f'{request_url.replace("https://", "")}'
            f'{query_params}'
            f'{content_type}'
            f'{nonce}'
            f'{timestamp}'
            'v2'
            f'{payload_string}'
        )
        
        # Create signature
        signature = hmac.new(
            self.api_secret,
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        headers = {
            'X-Auth': f'BITSTAMP {self.api_key}',
            'X-Auth-Version': 'v2',
            'X-Auth-Signature': signature,
            'X-Auth-Nonce': nonce,
            'X-Auth-Timestamp': timestamp,
        }
        
        if content_type:
            headers['Content-Type'] = content_type
        
        return headers
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Bitstamp trade data"""
        try:
            # Bitstamp user transaction format for trades
            trade_id = str(raw_trade['id'])
            timestamp = self._parse_bitstamp_timestamp(raw_trade['datetime'])
            
            # Find the trading pair (key with underscore)
            pair_key = None
            for key in raw_trade.keys():
                if '_' in key and key != 'order_id':
                    pair_key = key
                    break
            
            if not pair_key:
                return None
            
            # Parse pair (e.g., "btcusd", "ethusd")
            base_symbol, quote_symbol = pair_key.split('_', 1)
            base_asset = asset_from_exchange(base_symbol.upper(), self.location)
            quote_asset = asset_from_exchange(quote_symbol.upper(), self.location)
            
            # Get amounts
            base_amount = parse_decimal_safe(str(raw_trade.get(base_symbol, '0')))
            quote_amount = parse_decimal_safe(str(raw_trade.get(quote_symbol, '0')))
            
            # Determine trade type
            trade_type = TradeType.BUY if base_amount > 0 else TradeType.SELL
            
            # Get price and fee
            price = parse_decimal_safe(str(raw_trade.get(pair_key, '0')))
            fee_amount = parse_decimal_safe(str(raw_trade.get('fee', '0')))
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(abs(base_amount)))),
                rate=Price(Decimal(str(price))),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=quote_asset,  # Bitstamp fees are typically in quote currency
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing Bitstamp trade: {e}")
            return None
    
    def _parse_bitstamp_timestamp(self, datetime_str: str) -> Timestamp:
        """Parse Bitstamp datetime string to timestamp"""
        try:
            # Bitstamp format: "2023-01-01 12:00:00"
            from datetime import datetime
            dt = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
            return Timestamp(int(dt.timestamp()))
        except Exception:
            # Fallback to current time if parsing fails
            return Timestamp(int(time.time()))
