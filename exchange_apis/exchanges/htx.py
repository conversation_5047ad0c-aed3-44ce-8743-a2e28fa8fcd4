"""
HTX (formerly Huobi) exchange implementation
"""
import time
import hashlib
import hmac
import base64
import datetime
import urllib.parse
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class HTXExchange(BaseExchange):
    """HTX (formerly Huobi) exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.HTX,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.huobi.pro",
        )
        
        # HTX specific settings
        self.accounts: List[Dict[str, Any]] = []
        
    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate HTX API credentials"""
        try:
            accounts = self._get_accounts()
            if len(accounts) == 0:
                return False, "API key cannot access account information"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from HTX"""
        trades = []
        
        try:
            # HTX limits queries to 120 days
            max_days = 120
            earliest_ts = int(time.time()) - (max_days * 24 * 60 * 60)
            
            if end_timestamp <= earliest_ts:
                return []  # Query out of range
            
            if start_timestamp < earliest_ts:
                start_timestamp = Timestamp(earliest_ts + 900)  # 15 minutes safety margin
            
            # Query in 2-day chunks (HTX limitation)
            chunk_seconds = 2 * 24 * 60 * 60
            current_end = end_timestamp
            
            while current_end > start_timestamp:
                current_start = max(current_end - chunk_seconds, start_timestamp)
                
                # Convert to milliseconds
                start_ms = current_start * 1000
                end_ms = current_end * 1000
                
                params = {
                    'start-time': str(start_ms),
                    'end-time': str(end_ms),
                    'size': '100',
                }
                
                response = self._make_authenticated_request('v1/order/matchresults', params=params)
                
                if response.get('status') == 'ok':
                    data = response.get('data', [])
                    
                    for trade_data in data:
                        trade = self._parse_trade_data(trade_data)
                        if trade:
                            trades.append(trade)
                
                current_end = current_start
            
        except Exception as e:
            print(f"Error querying HTX trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_accounts(self) -> List[Dict[str, Any]]:
        """Get accounts for the API key"""
        if not self.accounts:
            response = self._make_authenticated_request('v1/account/accounts')
            if response.get('status') == 'ok':
                self.accounts = response.get('data', [])
        return self.accounts
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate HTX authentication headers"""
        # HTX uses query parameters for authentication, not headers
        return {}
    
    def _make_authenticated_request(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        method: str = 'GET',
    ) -> Dict[str, Any]:
        """Make authenticated request to HTX with signature in query params"""
        self.rate_limiter.wait_if_needed()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        # Create signed parameters
        signed_params = self._sign_request(url, params or {})
        
        return make_request(url, method=method, params=signed_params)
    
    def _sign_request(self, url: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create signature for HTX request"""
        new_params = params.copy()
        new_params['AccessKeyId'] = self.api_key
        new_params['SignatureVersion'] = '2'
        new_params['SignatureMethod'] = 'HmacSHA256'
        new_params['Timestamp'] = datetime.datetime.now(datetime.timezone.utc).strftime('%Y-%m-%dT%H:%M:%S')
        
        # Parse URL components
        parsed_url = urllib.parse.urlparse(url)
        host = parsed_url.hostname
        path = parsed_url.path
        
        # Create parameter string
        param_str = '&'.join([
            f"{k}={urllib.parse.quote_plus(str(v))}"
            for k, v in sorted(new_params.items())
            if v is not None
        ])
        
        # Create payload for signature
        payload = f"GET\n{host}\n{path}\n{param_str}"
        
        # Create signature
        signature = base64.b64encode(
            hmac.new(
                self.api_secret,
                payload.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode()
        
        new_params['Signature'] = signature
        return new_params
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse HTX trade data"""
        try:
            # HTX trade format
            trade_id = str(raw_trade['id'])
            timestamp = Timestamp(int(raw_trade['created-at']) // 1000)  # Convert from ms
            
            # Parse symbol and fee currency
            symbol = raw_trade['symbol']
            fee_currency = raw_trade['fee-currency']
            
            # Parse trade type
            trade_type_str = raw_trade['type'].split('-')[0]  # e.g., "buy-market" -> "buy"
            trade_type = TradeType.BUY if trade_type_str == 'buy' else TradeType.SELL
            
            # Parse assets based on fee currency and trade type
            base_asset, quote_asset = self._parse_htx_symbol(symbol, fee_currency, trade_type)
            
            # Trade details
            amount = parse_decimal_safe(str(raw_trade['filled-amount']))
            price = parse_decimal_safe(str(raw_trade['price']))
            fee_amount = parse_decimal_safe(str(raw_trade.get('filled-fees', '0')))
            
            # Fee asset depends on trade type
            fee_currency_asset = asset_from_exchange(fee_currency, self.location)
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(price))),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=fee_currency_asset,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing HTX trade: {e}")
            return None
    
    def _parse_htx_symbol(self, symbol: str, fee_currency: str, trade_type: TradeType) -> Tuple[Asset, Asset]:
        """Parse HTX trading symbol with fee currency context"""
        # HTX fee logic:
        # - Buy orders: fee in base currency
        # - Sell orders: fee in quote currency
        
        # Remove fee currency from symbol to get the other asset
        if symbol.endswith(fee_currency.lower()):
            other_currency = symbol[:-len(fee_currency)]
        elif symbol.startswith(fee_currency.lower()):
            other_currency = symbol[len(fee_currency):]
        else:
            # Fallback: assume last 3-4 characters are quote
            if len(symbol) >= 4 and symbol[-4:].upper() in ['USDT', 'USDC']:
                other_currency = symbol[:-4]
            else:
                other_currency = symbol[:-3]
        
        fee_asset = asset_from_exchange(fee_currency, self.location)
        other_asset = asset_from_exchange(other_currency.upper(), self.location)
        
        if trade_type == TradeType.BUY:
            # Buy: fee in base currency
            return fee_asset, other_asset  # fee_asset is base
        else:
            # Sell: fee in quote currency
            return other_asset, fee_asset  # fee_asset is quote
