"""
Kraken exchange implementation
"""
import time
import hashlib
import hmac
import base64
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import create_signature_base64, current_timestamp, parse_decimal_safe, make_request
from ..core.pair_parsing import KrakenPairParser
from ..core.asset_converters import asset_from_kraken
from .base import BaseExchange


class KrakenExchange(BaseExchange):
    """Kraken exchange implementation"""

    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.KRAKEN,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.kraken.com",
        )

        # Initialize pair parser
        self.pair_parser = KrakenPairParser()

        # Cache for API data
        self.assets: Dict[str, Dict[str, Any]] = {}
        self.asset_pairs: Dict[str, Dict[str, Any]] = {}
        self.asset_name_map: Dict[str, str] = {}
        self.api_data_loaded = False

    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Kraken API credentials"""
        try:
            response = self._make_authenticated_request('Balance', method='POST')
            if 'error' in response and response['error']:
                return False, f"API Error: {response['error']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"

    def _load_api_data(self):
        """Load Assets and AssetPairs data from Kraken API"""
        if self.api_data_loaded:
            return

        try:
            # Load Assets data (public endpoint, no auth needed)
            print("Loading Kraken assets...")
            assets_response = self._make_public_request('0/public/Assets')
            if 'error' in assets_response and assets_response['error']:
                print(f"Error loading Kraken assets: {assets_response['error']}")
            else:
                self.assets = assets_response.get('result', {})
                print(f"Loaded {len(self.assets)} Kraken assets")

            # Load AssetPairs data (public endpoint, no auth needed)
            print("Loading Kraken asset pairs...")
            pairs_response = self._make_public_request('0/public/AssetPairs')
            if 'error' in pairs_response and pairs_response['error']:
                print(f"Error loading Kraken asset pairs: {pairs_response['error']}")
            else:
                self.asset_pairs = pairs_response.get('result', {})
                print(f"Loaded {len(self.asset_pairs)} Kraken asset pairs")

            # Build asset name mapping
            self._build_asset_name_map()

            # Update pair parser with official data
            self._update_pair_parser()

            self.api_data_loaded = True

        except Exception as e:
            print(f"Failed to load Kraken API data: {e}")
            # Continue without API data - parser will use fallback logic

    def _make_public_request(self, endpoint: str) -> Dict[str, Any]:
        """Make public API request (no authentication needed)"""
        self.rate_limiter.wait_if_needed()

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        return make_request(url, method='GET')

    def _build_asset_name_map(self):
        """Build mapping from Kraken asset names to standard names"""
        for kraken_name, asset_info in self.assets.items():
            altname = asset_info.get('altname', kraken_name)

            # Use asset converter to get standard name
            try:
                standard_asset = asset_from_kraken(kraken_name)
                self.asset_name_map[kraken_name] = standard_asset.identifier

                # Also map altname if different
                if altname != kraken_name:
                    standard_asset_alt = asset_from_kraken(altname)
                    self.asset_name_map[altname] = standard_asset_alt.identifier

            except Exception:
                # Fallback to using as-is
                self.asset_name_map[kraken_name] = kraken_name
                if altname != kraken_name:
                    self.asset_name_map[altname] = altname

        print(f"Built asset name mapping for {len(self.asset_name_map)} assets")

    def _update_pair_parser(self):
        """Update pair parser with official API data"""
        for pair_name, pair_info in self.asset_pairs.items():
            if isinstance(pair_info, dict):
                base = pair_info.get('base', '')
                quote = pair_info.get('quote', '')
                if base and quote:
                    # Convert to standard names
                    base_standard = self.asset_name_map.get(base, base)
                    quote_standard = self.asset_name_map.get(quote, quote)

                    # Store the mapping in parser
                    self.pair_parser.known_pairs[pair_name] = (base_standard, quote_standard)

                    # Also store alternative names
                    alt_name = pair_info.get('altname', '')
                    if alt_name and alt_name != pair_name:
                        self.pair_parser.known_pairs[alt_name] = (base_standard, quote_standard)

        print(f"Updated pair parser with {len(self.pair_parser.known_pairs)} official pairs")

    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Kraken"""
        # Load API data first
        self._load_api_data()

        trades = []

        try:
            # Query trades from Kraken API
            params = {
                'type': 'all',
                'start': int(start_timestamp),
                'end': int(end_timestamp),
            }

            response = self._make_authenticated_request(
                '0/private/TradesHistory',
                method='POST',
                data=params
            )

            if 'error' in response and response['error']:
                raise Exception(f"Kraken API error: {response['error']}")

            trades_data = response.get('result', {}).get('trades', {})

            for trade_id, trade_data in trades_data.items():
                trade = self._parse_trade_data(trade_data)
                if trade:
                    trade.link = trade_id  # Set Kraken trade ID
                    trades.append(trade)

        except Exception as e:
            print(f"Error querying Kraken trades: {e}")

        return trades

    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Kraken authentication headers"""
        if not self.api_secret:
            return {}

        # Generate nonce (milliseconds since epoch)
        nonce = int(1000 * time.time())

        # Prepare request data
        req = data.copy() if data else {}
        req['nonce'] = nonce

        # Create POST data
        postdata = urlencode(req)

        # Create URL path for private API
        urlpath = f'/0/private/{endpoint}'

        # Create signature following Kraken's specification
        # Message = urlpath + sha256(nonce + postdata)
        hashable = (str(nonce) + postdata).encode()
        message = urlpath.encode() + hashlib.sha256(hashable).digest()

        signature = hmac.new(
            base64.b64decode(self.api_secret),
            message,
            hashlib.sha512,
        )

        return {
            'API-Key': self.api_key,
            'API-Sign': base64.b64encode(signature.digest()).decode(),
            'Content-Type': 'application/x-www-form-urlencoded',
        }

    def _make_authenticated_request(
        self,
        endpoint: str,
        method: str = 'POST',
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make authenticated API request to Kraken

        Kraken requires POST requests for private endpoints with data in the body
        """
        self.rate_limiter.wait_if_needed()

        # Kraken private API endpoints
        url = f"{self.base_url}/0/private/{endpoint}"
        headers = self.session_headers.copy()

        # Prepare request data
        req = data.copy() if data else {}

        # Add authentication headers (this will also add nonce to req)
        auth_headers = self._get_auth_headers(endpoint, method, params, req)
        headers.update(auth_headers)

        # For Kraken, we need to send the data (including nonce) as POST body
        post_data = urlencode(req)

        print(url)
        return make_request(url, method, params=None, data=post_data, headers=headers)

    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Kraken trade data"""
        try:
            # Parse pair using the comprehensive parser
            pair = raw_trade.get('pair', '')
            base_asset, quote_asset = self.pair_parser.parse_pair(pair)

            # Parse trade type
            trade_type_str = raw_trade.get('type', '').lower()
            trade_type = TradeType.BUY if trade_type_str == 'buy' else TradeType.SELL

            # Parse amounts and prices
            amount = AssetAmount(Decimal(str(raw_trade.get('vol', '0'))))
            rate = Price(Decimal(str(raw_trade.get('price', '0'))))

            # Parse fee
            fee_amount = parse_decimal_safe(raw_trade.get('fee', '0'))
            fee = Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None

            # Parse timestamp
            timestamp = Timestamp(int(float(raw_trade.get('time', '0'))))

            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=amount,
                rate=rate,
                fee=fee,
                fee_currency=quote_asset if fee else None,
            )

        except Exception as e:
            print(f"Error parsing Kraken trade: {e}")
            return None

    def get_supported_pairs(self) -> List[str]:
        """Get list of supported trading pairs from API data"""
        self._load_api_data()
        return list(self.asset_pairs.keys())

    def get_asset_info(self, asset_name: str) -> Optional[Dict[str, Any]]:
        """Get asset information from API data"""
        self._load_api_data()
        return self.assets.get(asset_name)

    def get_pair_info(self, pair_name: str) -> Optional[Dict[str, Any]]:
        """Get pair information from API data"""
        self._load_api_data()
        return self.asset_pairs.get(pair_name)
