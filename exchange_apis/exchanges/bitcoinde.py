"""
Bitcoin.de exchange implementation
"""
import time
import hashlib
import hmac
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange

# MD5 hash of empty string used in Bitcoin.de signature generation
MD5_EMPTY_STR = 'd41d8cd98f00b204e9800998ecf8427e'


class BitcoindeExchange(BaseExchange):
    """Bitcoin.de exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.BITCOINDE,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.bitcoin.de",
        )
        
        # Bitcoin.de specific settings
        self.api_version = "v4"
        
    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate Bitcoin.de API credentials"""
        try:
            response = self._make_authenticated_request('account')
            if 'errors' in response:
                return False, f"API Error: {response['errors']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Bitcoin.de"""
        trades = []
        
        try:
            # Bitcoin.de uses pagination
            page = 1
            all_trades = []
            
            while True:
                response = self._make_authenticated_request('trades', params={'state': 1, 'page': page})
                
                if 'trades' not in response:
                    break
                
                all_trades.extend(response['trades'])
                
                # Check if we have more pages
                if 'page' not in response:
                    break
                
                page_info = response['page']
                if page_info['current'] >= page_info['last']:
                    break
                
                page = page_info['current'] + 1
            
            # Filter trades by timestamp and parse
            for trade_data in all_trades:
                if trade_data.get('state') != 1:  # Only completed trades
                    continue
                
                trade = self._parse_trade_data(trade_data)
                if trade and start_timestamp <= trade.timestamp <= end_timestamp:
                    trades.append(trade)
            
        except Exception as e:
            print(f"Error querying Bitcoin.de trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Bitcoin.de authentication headers"""
        if not self.api_secret:
            return {}
        
        # Bitcoin.de uses millisecond timestamps as nonce
        nonce = str(int(time.time() * 1000))
        
        # Create the request URL
        api_path = f"/{self.api_version}/{endpoint.lstrip('/')}"
        if params:
            api_path += f"?{urlencode(params)}"
        
        request_url = f"{self.base_url}{api_path}"
        
        # Create the message for signature
        # Format: METHOD#URL#API_KEY#NONCE#MD5_EMPTY_STR
        message = f"{method.upper()}#{request_url}#{self.api_key}#{nonce}#{MD5_EMPTY_STR}"
        
        # Create signature
        signature = hmac.new(
            self.api_secret,
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return {
            'x-api-key': self.api_key,
            'x-api-nonce': nonce,
            'x-api-signature': signature,
        }
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Bitcoin.de trade data"""
        try:
            # Bitcoin.de trade format
            trade_id = str(raw_trade['trade_id'])
            
            # Parse timestamp
            timestamp_str = raw_trade.get('successfully_finished_at') or raw_trade.get('trade_marked_as_paid_at')
            timestamp = self._parse_bitcoinde_timestamp(timestamp_str)
            
            # Parse trading pair
            trading_pair = raw_trade['trading_pair']
            base_asset, quote_asset = self._parse_bitcoinde_pair(trading_pair)
            
            # Trade details
            trade_type = TradeType.BUY if raw_trade['type'] == 'buy' else TradeType.SELL
            
            # Amounts
            tx_amount = parse_decimal_safe(str(raw_trade['amount_currency_to_trade']))
            native_amount = parse_decimal_safe(str(raw_trade['volume_currency_to_pay']))
            fee_amount = parse_decimal_safe(str(raw_trade['fee_currency_to_pay']))
            
            # Calculate rate
            rate = native_amount / tx_amount if tx_amount > 0 else Decimal('0')
            
            # Fee is typically in EUR
            fee_currency = asset_from_exchange('EUR', self.location)
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(tx_amount))),
                rate=Price(Decimal(str(rate))),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=fee_currency,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing Bitcoin.de trade: {e}")
            return None
    
    def _parse_bitcoinde_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """Parse Bitcoin.de trading pair"""
        # Bitcoin.de pairs are like "btceur", "etheur", "ltceur"
        if len(pair) == 6:
            # 3-3 split (e.g., "btceur")
            base_str = pair[:3]
            quote_str = pair[3:]
        elif len(pair) in [7, 8]:
            # 4-3 or 4-4 split (e.g., "dasheur")
            base_str = pair[:4]
            quote_str = pair[4:]
        else:
            # Fallback
            raise ValueError(f"Unexpected Bitcoin.de pair format: {pair}")
        
        # Convert to assets
        base_asset = asset_from_exchange(base_str.upper(), self.location)
        quote_asset = asset_from_exchange(quote_str.upper(), self.location)
        
        return base_asset, quote_asset
    
    def _parse_bitcoinde_timestamp(self, timestamp_str: str) -> Timestamp:
        """Parse Bitcoin.de timestamp string"""
        try:
            # Bitcoin.de uses ISO 8601 format
            from datetime import datetime
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            return Timestamp(int(dt.timestamp()))
        except Exception:
            # Fallback to current time if parsing fails
            return Timestamp(int(time.time()))
