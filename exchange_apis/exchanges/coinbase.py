"""
Coinbase exchange implementation
"""
import time
import hashlib
import hmac
import base64
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe
from ..core.asset_converters import asset_from_coinbase
from .base import BaseExchange


class CoinbaseExchange(BaseExchange):
    """Coinbase exchange implementation"""

    def __init__(self, name: str, api_key: str, api_secret: bytes, passphrase: str = "", **kwargs):
        super().__init__(
            name=name,
            location=Location.COINBASE,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=10.0,  # 10 calls per second
            base_url="https://api.coinbase.com",
        )
        self.passphrase = passphrase

    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Coinbase API credentials"""
        try:
            response = self._make_authenticated_request('v2/accounts')
            if 'errors' in response:
                return False, f"API Error: {response['errors']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"

    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Coinbase"""
        trades = []

        try:
            # Get all accounts
            accounts_response = self._make_authenticated_request('v2/accounts')
            accounts = accounts_response.get('data', [])

            # Query transactions for each account
            for account in accounts:
                account_id = account.get('id')
                if not account_id:
                    continue

                try:
                    account_trades = self._query_account_transactions(
                        account_id, start_timestamp, end_timestamp
                    )
                    trades.extend(account_trades)
                except Exception as e:
                    print(f"Error querying transactions for account {account_id}: {e}")
                    continue

        except Exception as e:
            print(f"Error querying Coinbase trades: {e}")

        return sorted(trades, key=lambda t: t.timestamp)

    def _query_account_transactions(
        self,
        account_id: str,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query transactions for a specific account"""
        trades = []

        # Coinbase uses pagination
        next_uri = f'v2/accounts/{account_id}/transactions'

        while next_uri:
            response = self._make_authenticated_request(next_uri)

            transactions = response.get('data', [])
            pagination = response.get('pagination', {})

            for transaction in transactions:
                # Filter by timestamp
                created_at = transaction.get('created_at', '')
                if created_at:
                    # Parse ISO timestamp to unix timestamp
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    tx_timestamp = Timestamp(int(dt.timestamp()))

                    if tx_timestamp < start_timestamp or tx_timestamp > end_timestamp:
                        continue

                # Only process buy/sell transactions
                tx_type = transaction.get('type', '')
                if tx_type in ['buy', 'sell']:
                    trade = self._parse_transaction_data(transaction)
                    if trade:
                        trades.append(trade)

            # Check for next page
            next_uri = pagination.get('next_uri')
            if next_uri and not next_uri.startswith('http'):
                next_uri = next_uri.lstrip('/')

        return trades

    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Coinbase authentication headers"""
        if not self.api_secret:
            return {}

        timestamp = str(int(time.time()))

        # Create message for signature
        message = timestamp + method.upper() + '/' + endpoint.lstrip('/')
        if data:
            import json
            message += json.dumps(data)

        # Create signature
        signature = hmac.new(
            self.api_secret,
            message.encode(),
            hashlib.sha256
        ).hexdigest()

        return {
            'CB-ACCESS-KEY': self.api_key,
            'CB-ACCESS-SIGN': signature,
            'CB-ACCESS-TIMESTAMP': timestamp,
            'CB-ACCESS-PASSPHRASE': self.passphrase,
            'CB-VERSION': '2021-06-25',
        }

    def _parse_transaction_data(self, raw_transaction: Dict[str, Any]) -> Optional[Trade]:
        """Parse Coinbase transaction data"""
        try:
            tx_type = raw_transaction.get('type', '')
            if tx_type not in ['buy', 'sell']:
                return None

            # Parse trade type
            trade_type = TradeType.BUY if tx_type == 'buy' else TradeType.SELL

            # Parse amounts
            amount_data = raw_transaction.get('amount', {})
            amount_value = parse_decimal_safe(amount_data.get('amount', '0'))
            amount_currency = amount_data.get('currency', '')

            # Parse native amount (usually fiat)
            native_amount_data = raw_transaction.get('native_amount', {})
            native_value = parse_decimal_safe(native_amount_data.get('amount', '0'))
            native_currency = native_amount_data.get('currency', 'USD')

            # Determine base and quote assets
            if trade_type == TradeType.BUY:
                base_asset = Asset(amount_currency)
                quote_asset = Asset(native_currency)
                amount = AssetAmount(Decimal(str(abs(amount_value))))
                rate = Price(Decimal(str(abs(native_value) / abs(amount_value)))) if amount_value != 0 else Price(Decimal('0'))
            else:  # SELL
                base_asset = Asset(amount_currency)
                quote_asset = Asset(native_currency)
                amount = AssetAmount(Decimal(str(abs(amount_value))))
                rate = Price(Decimal(str(abs(native_value) / abs(amount_value)))) if amount_value != 0 else Price(Decimal('0'))

            # Parse timestamp
            created_at = raw_transaction.get('created_at', '')
            if created_at:
                from datetime import datetime
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                timestamp = Timestamp(int(dt.timestamp()))
            else:
                timestamp = Timestamp(int(time.time()))

            # Parse fee (if available)
            fee_data = raw_transaction.get('fee', {})
            fee_amount = parse_decimal_safe(fee_data.get('amount', '0'))
            fee = Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None
            fee_currency = Asset(fee_data.get('currency', native_currency)) if fee else None

            # Transaction ID
            tx_id = raw_transaction.get('id', '')

            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=amount,
                rate=rate,
                fee=fee,
                fee_currency=fee_currency,
                link=tx_id,
            )

        except Exception as e:
            print(f"Error parsing Coinbase transaction: {e}")
            return None
