"""
Poloniex exchange implementation
"""
import time
import hashlib
import hmac
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class PoloniexExchange(BaseExchange):
    """Poloniex exchange implementation"""

    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.POLONIEX,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.poloniex.com",
        )

    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Poloniex API credentials"""
        try:
            response = self._make_authenticated_request('tradingApi', data={'command': 'returnBalances'})
            if 'error' in response:
                return False, f"API Error: {response['error']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"

    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Poloniex"""
        trades = []

        try:
            # Poloniex uses Unix timestamps
            data = {
                'command': 'returnTradeHistory',
                'currencyPair': 'all',
                'start': str(start_timestamp),
                'end': str(end_timestamp),
                'limit': '10000',
            }

            response = self._make_authenticated_request('tradingApi', data=data)

            if isinstance(response, dict):
                for pair, pair_trades in response.items():
                    if isinstance(pair_trades, list):
                        for trade_data in pair_trades:
                            trade = self._parse_trade_data(trade_data, pair)
                            if trade:
                                trades.append(trade)

        except Exception as e:
            print(f"Error querying Poloniex trades: {e}")

        return sorted(trades, key=lambda t: t.timestamp)

    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Poloniex authentication headers"""
        if not self.api_secret:
            return {}

        # Poloniex requires data for authentication
        if not data:
            data = {}

        # Poloniex uses nonce
        nonce = str(int(time.time() * 1000))
        data['nonce'] = nonce

        # Create POST data
        post_data = urlencode(data)

        # Create signature
        signature = hmac.new(
            self.api_secret,
            post_data.encode('utf-8'),
            hashlib.sha512
        ).hexdigest()

        return {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Key': self.api_key,
            'Sign': signature,
        }

    def _parse_trade_data(self, raw_trade: Dict[str, Any], pair: str) -> Optional[Trade]:
        """Parse Poloniex trade data"""
        try:
            # Poloniex trade format
            trade_id = str(raw_trade['tradeID'])
            timestamp = self._parse_poloniex_timestamp(raw_trade['date'])

            # Parse pair (e.g., "BTC_ETH")
            base_asset, quote_asset = self._parse_poloniex_pair(pair)

            # Trade details
            trade_type = TradeType.BUY if raw_trade['type'] == 'buy' else TradeType.SELL
            amount = parse_decimal_safe(str(raw_trade['amount']))
            rate = parse_decimal_safe(str(raw_trade['rate']))
            fee_amount = parse_decimal_safe(str(raw_trade['fee']))

            # Poloniex fees are in the quote currency for buys, base currency for sells
            fee_currency = quote_asset if trade_type == TradeType.BUY else base_asset

            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(rate))),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=fee_currency,
                link=trade_id,
            )

        except Exception as e:
            print(f"Error parsing Poloniex trade: {e}")
            return None

    def _parse_poloniex_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """Parse Poloniex trading pair"""
        # Poloniex uses underscore separator (e.g., "BTC_ETH")
        if '_' in pair:
            quote_str, base_str = pair.split('_', 1)  # Note: Poloniex format is QUOTE_BASE
        else:
            # Fallback for unexpected format
            raise ValueError(f"Unexpected Poloniex pair format: {pair}")

        # Convert to assets
        base_asset = asset_from_exchange(base_str, self.location)
        quote_asset = asset_from_exchange(quote_str, self.location)

        return base_asset, quote_asset

    def _parse_poloniex_timestamp(self, date_str: str) -> Timestamp:
        """Parse Poloniex date string to timestamp"""
        try:
            # Poloniex format: "2023-01-01 12:00:00"
            from datetime import datetime
            dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            return Timestamp(int(dt.timestamp()))
        except Exception:
            # Fallback to current time if parsing fails
            return Timestamp(int(time.time()))
