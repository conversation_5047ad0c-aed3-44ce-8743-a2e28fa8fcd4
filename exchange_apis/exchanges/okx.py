"""
OKX exchange implementation
"""
import time
import hashlib
import hmac
import base64
import datetime
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from decimal import Decimal
from urllib.parse import urlencode, urljoin
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class OKXExchange(BaseExchange):
    """OKX exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, passphrase: str, **kwargs):
        super().__init__(
            name=name,
            location=Location.OKX,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://www.okx.com",
        )
        
        # OKX specific settings
        self.passphrase = passphrase
        self.max_results = 100
        
    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate OKX API credentials"""
        try:
            response = self._make_authenticated_request('api/v5/account/balance')
            if response.get('code') != '0':
                return False, f"API Error: {response.get('msg', 'Unknown error')}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from OKX"""
        trades = []
        
        try:
            # OKX uses milliseconds
            start_ms = start_timestamp * 1000
            end_ms = end_timestamp * 1000
            
            # Query filled orders
            all_trades = self._query_paginated_trades(start_ms, end_ms)
            
            for trade_data in all_trades:
                trade = self._parse_trade_data(trade_data)
                if trade:
                    trades.append(trade)
            
        except Exception as e:
            print(f"Error querying OKX trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _query_paginated_trades(self, start_ms: int, end_ms: int) -> List[Dict[str, Any]]:
        """Query trades with pagination"""
        all_trades = []
        after = None
        
        while True:
            params = {
                'instType': 'SPOT',
                'state': 'filled',
                'begin': str(start_ms),
                'end': str(end_ms),
                'limit': str(self.max_results),
            }
            
            if after:
                params['after'] = after
            
            response = self._make_authenticated_request('api/v5/trade/orders-history-archive', params=params)
            
            if response.get('code') != '0':
                break
            
            data = response.get('data', [])
            if not data:
                break
            
            all_trades.extend(data)
            
            # Check if we have more data
            if len(data) < self.max_results:
                break
            
            # Use the last order ID for pagination
            after = data[-1]['ordId']
        
        return all_trades
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate OKX authentication headers"""
        if not self.api_secret:
            return {}
        
        # OKX uses ISO timestamp
        timestamp = datetime.datetime.now(tz=datetime.timezone.utc).isoformat(timespec='milliseconds').replace('+00:00', 'Z')
        
        # Create the message for signature
        api_path = f"/{endpoint.lstrip('/')}"
        query_string = ''
        if params:
            query_string = urlencode(params)
            if query_string:
                api_path += f"?{query_string}"
        
        body = ''
        if data:
            import json
            body = json.dumps(data)
        
        message = f"{timestamp}{method.upper()}{api_path}{body}"
        
        # Create signature
        signature = base64.b64encode(
            hmac.new(
                self.api_secret,
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        return {
            'Content-Type': 'application/json',
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
        }
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse OKX trade data"""
        try:
            # OKX trade format
            trade_id = str(raw_trade['ordId'])
            timestamp = Timestamp(int(raw_trade['cTime']) // 1000)  # Convert from ms
            
            # Parse pair (e.g., "BTC-USDT")
            inst_id = raw_trade['instId']
            base_asset, quote_asset = self._parse_okx_pair(inst_id)
            
            # Trade details
            side = raw_trade['side']
            trade_type = TradeType.BUY if side == 'buy' else TradeType.SELL
            
            amount = parse_decimal_safe(str(raw_trade['accFillSz']))
            price = parse_decimal_safe(str(raw_trade['avgPx']))
            fee_amount = parse_decimal_safe(str(raw_trade['fee']))
            fee_currency_symbol = raw_trade['feeCcy']
            
            # OKX represents fees as negative numbers
            if fee_amount < 0:
                fee_amount = abs(fee_amount)
            
            # Parse fee
            fee = Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None
            fee_currency = asset_from_exchange(fee_currency_symbol, self.location) if fee else None
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(price))),
                fee=fee,
                fee_currency=fee_currency,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing OKX trade: {e}")
            return None
    
    def _parse_okx_pair(self, inst_id: str) -> Tuple[Asset, Asset]:
        """Parse OKX trading pair"""
        # OKX uses dash separator (e.g., "BTC-USDT")
        if '-' in inst_id:
            base_str, quote_str = inst_id.split('-', 1)
        else:
            # Fallback for unexpected format
            raise ValueError(f"Unexpected OKX pair format: {inst_id}")
        
        # Convert to assets
        base_asset = asset_from_exchange(base_str, self.location)
        quote_asset = asset_from_exchange(quote_str, self.location)
        
        return base_asset, quote_asset
