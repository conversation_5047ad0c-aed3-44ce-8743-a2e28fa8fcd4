"""
Minimal Exchange API Library for Trade Export

This package provides a lightweight interface to various cryptocurrency exchange APIs
focused specifically on trade history export functionality.

Key Features:
- Unified Trade object across all exchanges
- Minimal dependencies
- Support for major exchanges (Kraken, Binance, Coinbase, etc.)
- Rate limiting and error handling
"""

from typing import Tuple
from .core.types import Location, TradeType, Timestamp, Asset
from .core.trade import Trade
from .core.asset_converters import asset_from_kraken, asset_from_binance, asset_from_coinbase, asset_from_exchange
from .core.pair_parsing import UniversalPairParser
from .exchanges.base import BaseExchange

# Import all exchange implementations
from .exchanges.kraken import KrakenExchange
from .exchanges.binance import BinanceExchange
from .exchanges.coinbase import CoinbaseExchange
from .exchanges.bitfinex import BitfinexExchange
from .exchanges.bitstamp import BitstampExchange
from .exchanges.kucoin import KuCoinExchange
from .exchanges.gemini import GeminiExchange
from .exchanges.okx import OKXExchange
from .exchanges.poloniex import PoloniexExchange
from .exchanges.bitcoinde import BitcoindeExchange
from .exchanges.bitmex import BitmexExchange
from .exchanges.bitpanda import BitpandaExchange
from .exchanges.bybit import BybitExchange
from .exchanges.htx import HTXExchange
from .exchanges.iconomi import IconomiExchange

__version__ = "1.0.0"

# Convenience function for validate_api_key compatibility
def validate_api_key(exchange_instance) -> Tuple[bool, str]:
    """
    Validate API key for any exchange instance

    Args:
        exchange_instance: Any exchange instance from this package

    Returns:
        Tuple of (success: bool, message: str)
    """
    return exchange_instance.validate_api_key()

__all__ = [
    # Core types and classes
    "Location",
    "TradeType",
    "Timestamp",
    "Asset",
    "Trade",
    "BaseExchange",

    # Utility functions
    "validate_api_key",

    # Asset converters
    "asset_from_kraken",
    "asset_from_binance",
    "asset_from_coinbase",
    "asset_from_exchange",

    # Pair parsing
    "UniversalPairParser",

    # Exchange implementations
    "KrakenExchange",
    "BinanceExchange",
    "CoinbaseExchange",
    "BitfinexExchange",
    "BitstampExchange",
    "KuCoinExchange",
    "GeminiExchange",
    "OKXExchange",
    "PoloniexExchange",
    "BitcoindeExchange",
    "BitmexExchange",
    "BitpandaExchange",
    "BybitExchange",
    "HTXExchange",
    "IconomiExchange",
]
